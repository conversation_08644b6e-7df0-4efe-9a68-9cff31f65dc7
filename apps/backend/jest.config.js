const baseConfig = require("../../jest.config.base");

/** @type {import('jest').Config} */
module.exports = {
    ...baseConfig,
    displayName: "backend",
    testEnvironment: "node",
    setupFilesAfterEnv: ["<rootDir>/tests/jest.setup.js"],
    moduleNameMapper: {
        ...baseConfig.moduleNameMapper,
        "^@strapi/strapi$": "<rootDir>/node_modules/@strapi/strapi",
    },
    testPathIgnorePatterns: ["/node_modules/", ".tmp", ".cache", "build", "dist"],
    coveragePathIgnorePatterns: [
        "/node_modules/",
        "/database/",
        "/public/",
        "/build/",
        ".cache",
        ".tmp",
    ],
};
