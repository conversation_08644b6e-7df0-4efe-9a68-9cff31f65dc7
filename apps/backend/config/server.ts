export default ({ env }) => ({
    host: env("HOST", "0.0.0.0"),
    port: env.int("PORT", 1337),
    app: {
        keys: env.array("APP_KEYS"),
    },
    webhooks: {
        populateRelations: env.bool("WEBHOOKS_POPULATE_RELATIONS", false),
    },
    url: env("PUBLIC_URL", "http://localhost:1337"),
    admin: {
        auth: {
            secret: env("ADMIN_JWT_SECRET"),
        },
    },
    transfer: {
        token: {
            salt: env("TRANSFER_TOKEN_SALT"),
        },
    },
    apiToken: {
        salt: env("API_TOKEN_SALT"),
    },
});
