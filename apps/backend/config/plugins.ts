export default ({ env }) => ({
    email: {
        config: {
            provider: "sendgrid",
            providerOptions: {
                apiKey: env("EMAIL_SENDGRID_API_KEY"),
            },
            settings: {
                defaultFrom: env("EMAIL_DEFAULT_FROM", "<EMAIL>"),
                defaultReplyTo: env("EMAIL_DEFAULT_REPLY_TO", "<EMAIL>"),
            },
        },
    },
    upload: {
        config: {
            providerOptions: {
                localServer: {
                    maxage: 300000,
                },
            },
        },
    },
    // Custom GDPR plugin
    gdpr: {
        enabled: true,
        resolve: "./src/plugins/gdpr",
        config: {
            enabled: env.bool("GDPR_ENABLED", true),
            dataRetentionDays: env.int("GDPR_DATA_RETENTION_DAYS", 365),
            anonymizeOnDelete: env.bool("GDPR_ANONYMIZE_ON_DELETE", true),
        },
    },
    // Custom Audit Log plugin
    "audit-log": {
        enabled: true,
        resolve: "./src/plugins/audit-log",
        config: {
            retentionDays: env.int("AUDIT_LOG_RETENTION_DAYS", 90),
            exportFormats: ["json", "csv"],
            logIpAddress: env.bool("AUDIT_LOG_IP_ADDRESS", true),
            logUserAgent: env.bool("AUDIT_LOG_USER_AGENT", true),
        },
    },
});
