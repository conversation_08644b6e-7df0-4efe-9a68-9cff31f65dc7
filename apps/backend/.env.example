# Strapi Backend Environment Variables

# Server Configuration
HOST=0.0.0.0
PORT=1337
APP_KEYS=your-app-key-1,your-app-key-2
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
JWT_SECRET=your-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt

# Database Configuration
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=civicpoll
DATABASE_USERNAME=civicpoll_user
DATABASE_PASSWORD=your-secure-password
DATABASE_SSL=false
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Email Configuration (SendGrid)
EMAIL_PROVIDER=sendgrid
EMAIL_SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_DEFAULT_FROM=<EMAIL>
EMAIL_DEFAULT_REPLY_TO=<EMAIL>

# Media Upload Configuration
# Local storage is used by default
# For production, consider using a cloud storage provider

# Frontend URL (for CORS and email links)
FRONTEND_URL=http://localhost:3000

# Admin Panel URL
ADMIN_URL=http://localhost:1337/admin

# Node Environment
NODE_ENV=development

# Optional: Redis Configuration (for future use)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your-redis-password

# Optional: Sentry Configuration (for error tracking)
# SENTRY_DSN=your-sentry-dsn

# Optional: Rate Limiting
# RATE_LIMIT_INTERVAL=60000
# RATE_LIMIT_MAX=10

# GDPR Configuration
GDPR_ENABLED=true
GDPR_DATA_RETENTION_DAYS=365
GDPR_ANONYMIZE_ON_DELETE=true

# Audit Log Configuration
AUDIT_LOG_RETENTION_DAYS=90
AUDIT_LOG_IP_ADDRESS=true
AUDIT_LOG_USER_AGENT=true

# Security Configuration
ENCRYPTION_KEY=your-encryption-key-here

# Transfer Token Configuration (for Strapi Cloud)
TRANSFER_TOKEN_SALT=your-transfer-token-salt

# Public URL (for production)
PUBLIC_URL=https://civicpoll.fr.smatflow.xyz

# Cache Configuration (Redis)
CACHE_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Monitoring Configuration
PROMETHEUS_ENABLED=true
METRICS_ENDPOINT_ENABLED=true

# PostGIS Configuration
POSTGIS_ENABLED=true

# SSL Configuration for production
SSL_CERT_PATH=/etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/privkey.pem