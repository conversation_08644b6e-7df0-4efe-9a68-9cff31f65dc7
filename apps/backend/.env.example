# Strapi Backend Environment Variables

# Server Configuration
HOST=0.0.0.0
PORT=1337
APP_KEYS=your-app-key-1,your-app-key-2
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
JWT_SECRET=your-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt

# Database Configuration
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=civicpoll
DATABASE_USERNAME=civicpoll_user
DATABASE_PASSWORD=your-secure-password
DATABASE_SSL=false
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Email Configuration (SendGrid)
EMAIL_PROVIDER=sendgrid
EMAIL_SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_DEFAULT_FROM=<EMAIL>
EMAIL_DEFAULT_REPLY_TO=<EMAIL>

# Media Upload Configuration
# Local storage is used by default
# For production, consider using a cloud storage provider

# Frontend URL (for CORS and email links)
FRONTEND_URL=http://localhost:3000

# Admin Panel URL
ADMIN_URL=http://localhost:1337/admin

# Node Environment
NODE_ENV=development

# Optional: Redis Configuration (for future use)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your-redis-password

# Optional: Sentry Configuration (for error tracking)
# SENTRY_DSN=your-sentry-dsn

# Optional: Rate Limiting
# RATE_LIMIT_INTERVAL=60000
# RATE_LIMIT_MAX=10