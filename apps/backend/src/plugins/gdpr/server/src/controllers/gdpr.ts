import { Core } from "@strapi/strapi";

const controller = ({ strapi }: { strapi: Core.Strapi }) => ({
    /**
     * Export user data with async processing and format support
     */
    async exportData(ctx) {
        try {
            const { userId } = ctx.params;
            const { format = "json", async = false } = ctx.query;

            if (!userId) {
                return ctx.badRequest("User ID is required");
            }

            // Check permissions - find user by documentId
            const users = await strapi.documents("plugin::users-permissions.user").findMany({
                filters: { id: userId },
                limit: 1,
            });

            const user = users?.[0];

            if (!user) {
                return ctx.notFound("User not found");
            }

            // Check if requesting user has permission or is the user themselves
            if (ctx.state.user.id !== parseInt(userId) && ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to export this data");
            }

            // For async processing, create a request record
            if (async === "true") {
                const request = await strapi.documents("plugin::gdpr.gdpr-request").create({
                    data: {
                        type: "export",
                        userId,
                        requestedBy: ctx.state.user.id,
                        status: "pending",
                        metadata: { format },
                    },
                });

                // Process asynchronously
                setImmediate(async () => {
                    try {
                        await strapi.entityService.update("plugin::gdpr.gdpr-request", request.id, {
                            data: { status: "processing" },
                        });

                        const exportedData = await strapi
                            .plugin("gdpr")
                            .service("gdpr")
                            .exportUserData(userId, format);

                        await strapi.documents("plugin::gdpr.gdpr-request").update({
                            documentId: request.documentId,
                            data: {
                                status: "completed",
                                result: exportedData,
                                completedAt: new Date(),
                            } as any,
                        });

                        // Log the export action
                        await strapi
                            .plugin("gdpr")
                            .service("gdpr")
                            .logAction({
                                action: "data_export",
                                userId,
                                performedBy: ctx.state.user.id,
                                details: { format, async: true, requestId: request.documentId },
                            });
                    } catch (error) {
                        await strapi.documents("plugin::gdpr.gdpr-request").update({
                            documentId: request.documentId,
                            data: {
                                status: "failed",
                                error: error.message,
                                completedAt: new Date(),
                            },
                        });
                        strapi.log.error("GDPR async export error:", error);
                    }
                });

                return (ctx.body = {
                    message: "Export request created successfully",
                    requestId: request.documentId,
                    status: "pending",
                });
            }

            // Synchronous export
            const exportedData = await strapi
                .plugin("gdpr")
                .service("gdpr")
                .exportUserData(userId, format);

            // Log the export action
            await strapi
                .plugin("gdpr")
                .service("gdpr")
                .logAction({
                    action: "data_export",
                    userId,
                    performedBy: ctx.state.user.id,
                    details: { format, async: false },
                    ipAddress: ctx.request.ip,
                    userAgent: ctx.request.headers["user-agent"],
                });

            // Set appropriate headers for CSV download
            if (format === "csv") {
                ctx.set("Content-Type", "text/csv");
                ctx.set(
                    "Content-Disposition",
                    `attachment; filename="user-data-${userId}-${Date.now()}.csv"`,
                );
            }

            ctx.body = exportedData;
        } catch (error) {
            strapi.log.error("GDPR data export error:", error);
            return ctx.internalServerError("Failed to export data");
        }
    },

    /**
     * Delete user data
     */
    async deleteData(ctx) {
        try {
            const { userId } = ctx.params;

            if (!userId) {
                return ctx.badRequest("User ID is required");
            }

            // Check permissions
            const users = await strapi.documents("plugin::users-permissions.user").findMany({
                filters: { id: userId },
                limit: 1,
            });

            const user = users?.[0];

            if (!user) {
                return ctx.notFound("User not found");
            }

            // Check if requesting user has permission or is the user themselves
            if (ctx.state.user.id !== parseInt(userId) && ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to delete this data");
            }

            const result = await strapi.plugin("gdpr").service("gdpr").deleteUserData(userId);

            // Log the deletion action
            await strapi
                .plugin("gdpr")
                .service("gdpr")
                .logAction({
                    action: "data_deletion",
                    userId,
                    performedBy: ctx.state.user.id,
                    details: { deletedItems: result.deletedItems },
                });

            ctx.body = {
                message: "User data deleted successfully",
                deletedItems: result.deletedItems,
            };
        } catch (error) {
            strapi.log.error("GDPR data deletion error:", error);
            return ctx.internalServerError("Failed to delete data");
        }
    },

    /**
     * Anonymize user data with transaction support
     */
    async anonymizeData(ctx) {
        try {
            const { userId } = ctx.params;
            const { async = false } = ctx.query;

            if (!userId) {
                return ctx.badRequest("User ID is required");
            }

            // Check permissions
            const users = await strapi.documents("plugin::users-permissions.user").findMany({
                filters: { id: userId },
                limit: 1,
            });

            const user = users?.[0];

            if (!user) {
                return ctx.notFound("User not found");
            }

            // Check if requesting user has permission or is the user themselves
            if (ctx.state.user.id !== parseInt(userId) && ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to anonymize this data");
            }

            // Check if user can be anonymized
            const canAnonymize = await strapi
                .plugin("gdpr")
                .service("gdpr")
                .canAnonymizeUser(userId);
            if (!canAnonymize.allowed) {
                return ctx.badRequest(
                    canAnonymize.reason || "User cannot be anonymized at this time",
                );
            }

            // For async processing
            if (async === "true") {
                const request = await strapi.documents("plugin::gdpr.gdpr-request").create({
                    data: {
                        type: "anonymize",
                        userId,
                        requestedBy: ctx.state.user.id,
                        status: "pending",
                        metadata: {},
                    },
                });

                setImmediate(async () => {
                    try {
                        await strapi.documents("plugin::gdpr.gdpr-request").update({
                            documentId: request.documentId,
                            data: { status: "processing" },
                        });

                        const result = await strapi
                            .plugin("gdpr")
                            .service("gdpr")
                            .anonymizeUserData(userId);

                        await strapi.documents("plugin::gdpr.gdpr-request").update({
                            documentId: request.documentId,
                            data: {
                                status: "completed",
                                result,
                                completedAt: new Date(),
                            },
                        });

                        // Log the anonymization action
                        await strapi
                            .plugin("gdpr")
                            .service("gdpr")
                            .logAction({
                                action: "data_anonymization",
                                userId,
                                performedBy: ctx.state.user.id,
                                details: {
                                    anonymizedFields: result.anonymizedFields,
                                    statistics: result.statistics,
                                    async: true,
                                    requestId: request.documentId,
                                },
                                ipAddress: ctx.request.ip,
                                userAgent: ctx.request.headers["user-agent"],
                            });
                    } catch (error) {
                        await strapi.documents("plugin::gdpr.gdpr-request").update({
                            documentId: request.documentId,
                            data: {
                                status: "failed",
                                error: error.message,
                                completedAt: new Date(),
                            },
                        });
                        strapi.log.error("GDPR async anonymization error:", error);
                    }
                });

                return (ctx.body = {
                    message: "Anonymization request created successfully",
                    requestId: request.documentId,
                    status: "pending",
                });
            }

            // Synchronous anonymization
            const result = await strapi.plugin("gdpr").service("gdpr").anonymizeUserData(userId);

            // Log the anonymization action
            await strapi
                .plugin("gdpr")
                .service("gdpr")
                .logAction({
                    action: "data_anonymization",
                    userId,
                    performedBy: ctx.state.user.id,
                    details: {
                        anonymizedFields: result.anonymizedFields,
                        statistics: result.statistics,
                        async: false,
                    },
                    ipAddress: ctx.request.ip,
                    userAgent: ctx.request.headers["user-agent"],
                });

            ctx.body = {
                message: "User data anonymized successfully",
                anonymizedFields: result.anonymizedFields,
                statistics: result.statistics,
            };
        } catch (error) {
            strapi.log.error("GDPR data anonymization error:", error);
            return ctx.internalServerError("Failed to anonymize data");
        }
    },

    /**
     * Get user consents
     */
    async getConsents(ctx) {
        try {
            const { userId } = ctx.params;

            if (!userId) {
                return ctx.badRequest("User ID is required");
            }

            // Check if requesting user has permission or is the user themselves
            if (ctx.state.user.id !== parseInt(userId) && ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to view these consents");
            }

            const consents = await strapi.plugin("gdpr").service("gdpr").getUserConsents(userId);

            ctx.body = consents;
        } catch (error) {
            strapi.log.error("GDPR get consents error:", error);
            return ctx.internalServerError("Failed to get consents");
        }
    },

    /**
     * Update user consents
     */
    async updateConsents(ctx) {
        try {
            const { userId } = ctx.params;
            const { consents } = ctx.request.body;

            if (!userId) {
                return ctx.badRequest("User ID is required");
            }

            if (!consents || !Array.isArray(consents)) {
                return ctx.badRequest("Consents array is required");
            }

            // Check if requesting user has permission or is the user themselves
            if (ctx.state.user.id !== parseInt(userId) && ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to update these consents");
            }

            const updatedConsents = await strapi
                .plugin("gdpr")
                .service("gdpr")
                .updateUserConsents(userId, consents);

            // Log the consent update
            await strapi.plugin("gdpr").service("gdpr").logAction({
                action: "consent_update",
                userId,
                performedBy: ctx.state.user.id,
                details: { consents },
            });

            ctx.body = updatedConsents;
        } catch (error) {
            strapi.log.error("GDPR update consents error:", error);
            return ctx.internalServerError("Failed to update consents");
        }
    },

    /**
     * Get audit logs with search and filtering
     */
    async getAuditLogs(ctx) {
        try {
            const {
                userId,
                action,
                startDate,
                endDate,
                page = 1,
                pageSize = 20,
                search,
            } = ctx.query;

            // Only admins can view audit logs
            if (ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to view audit logs");
            }

            const logs = await strapi
                .plugin("gdpr")
                .service("gdpr")
                .getAuditLogs({
                    userId,
                    action,
                    startDate,
                    endDate,
                    search,
                    page: parseInt(page),
                    pageSize: parseInt(pageSize),
                });

            ctx.body = logs;
        } catch (error) {
            strapi.log.error("GDPR get audit logs error:", error);
            return ctx.internalServerError("Failed to get audit logs");
        }
    },

    /**
     * Get GDPR request status
     */
    async getRequestStatus(ctx) {
        try {
            const { requestId } = ctx.params;

            if (!requestId) {
                return ctx.badRequest("Request ID is required");
            }

            const request = await strapi.documents("plugin::gdpr.gdpr-request").findOne({
                documentId: requestId,
                populate: ["requestedBy"],
            });

            if (!request) {
                return ctx.notFound("Request not found");
            }

            // Check permissions - user can view their own requests, admins can view all
            if (
                ctx.state.user.id !== request.requestedBy?.id &&
                ctx.state.user.role?.type !== "admin"
            ) {
                return ctx.forbidden("You do not have permission to view this request");
            }

            ctx.body = {
                id: request.documentId,
                type: request.type,
                status: request.status,
                userId: request.userId,
                requestedBy: request.requestedBy?.id,
                createdAt: request.createdAt,
                completedAt: request.completedAt,
                error: request.error,
                metadata: request.metadata,
            };
        } catch (error) {
            strapi.log.error("GDPR get request status error:", error);
            return ctx.internalServerError("Failed to get request status");
        }
    },

    /**
     * Export audit logs for compliance
     */
    async exportAuditLogs(ctx) {
        try {
            const { startDate, endDate, format = "csv" } = ctx.query;

            // Only admins can export audit logs
            if (ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to export audit logs");
            }

            const exportData = await strapi.plugin("gdpr").service("gdpr").exportAuditLogs({
                startDate,
                endDate,
                format,
            });

            // Set appropriate headers for download
            if (format === "csv") {
                ctx.set("Content-Type", "text/csv");
                ctx.set(
                    "Content-Disposition",
                    `attachment; filename="audit-logs-${Date.now()}.csv"`,
                );
            } else {
                ctx.set("Content-Type", "application/json");
                ctx.set(
                    "Content-Disposition",
                    `attachment; filename="audit-logs-${Date.now()}.json"`,
                );
            }

            ctx.body = exportData;
        } catch (error) {
            strapi.log.error("GDPR export audit logs error:", error);
            return ctx.internalServerError("Failed to export audit logs");
        }
    },

    /**
     * Get GDPR statistics
     */
    async getStatistics(ctx) {
        try {
            // Only admins can view statistics
            if (ctx.state.user.role?.type !== "admin") {
                return ctx.forbidden("You do not have permission to view statistics");
            }

            const statistics = await strapi.plugin("gdpr").service("gdpr").getStatistics();

            ctx.body = statistics;
        } catch (error) {
            strapi.log.error("GDPR get statistics error:", error);
            return ctx.internalServerError("Failed to get statistics");
        }
    },
});

export default controller;
