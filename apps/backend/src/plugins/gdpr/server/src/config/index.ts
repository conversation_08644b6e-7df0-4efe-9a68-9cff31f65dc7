export default {
    default: {
        enabled: true,
        dataRetentionDays: 365,
        anonymizeOnDelete: true,
        enableConsentTracking: true,
        enableDataExport: true,
        enableDataDeletion: true,
    },
    validator: (config: any) => {
        if (config.dataRetentionDays && config.dataRetentionDays < 1) {
            throw new Error("dataRetentionDays must be at least 1");
        }
    },
};
