import { Core } from "@strapi/strapi";
import crypto from "crypto";

export interface ExportFormat {
    format: "json" | "csv";
}

export interface AnonymizationResult {
    anonymizedFields: string[];
    statistics: {
        totalFields: number;
        tablesAffected: number;
        filesAnonymized: number;
    };
}

const service = ({ strapi }: { strapi: Core.Strapi }): any => ({
    /**
     * Export all user data with format support
     */
    async exportUserData(userId: string, format: string = "json") {
        const userData: any = {
            exportDate: new Date().toISOString(),
            userId,
            profile: {},
            content: {},
            consents: [],
            activities: [],
        };

        // Get user profile data
        const users = await strapi.documents("plugin::users-permissions.user").findMany({
            filters: { id: userId },
            populate: "*",
            limit: 1,
        });

        const user = users?.[0];

        if (!user) {
            throw new Error("User not found");
        }

        // Export user profile (excluding sensitive data like password)
        userData.profile = {
            id: user.id,
            username: user.username,
            email: user.email,
            confirmed: user.confirmed,
            blocked: user.blocked,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };

        // Export user-generated content (polls, votes, comments)
        // Polls created by user (if content type exists)
        try {
            const polls = await strapi.documents("api::poll.poll").findMany({
                filters: { author: userId },
                populate: "*",
            });
            userData.content.polls = polls || [];
        } catch (error) {
            userData.content.polls = [];
            strapi.log.debug("Poll content type not found, skipping poll export");
        }

        // Votes by user (if content type exists)
        try {
            const votes = await strapi.documents("api::vote.vote").findMany({
                filters: { user: userId },
                populate: "*",
            });
            userData.content.votes = votes || [];
        } catch (error) {
            userData.content.votes = [];
            strapi.log.debug("Vote content type not found, skipping vote export");
        }

        // Comments by user (if you have a comment content type)
        try {
            const comments = await strapi.documents("api::comment.comment").findMany({
                filters: { author: userId },
                populate: "*",
            });
            userData.content.comments = comments || [];
        } catch (error) {
            // Comments might not exist
            userData.content.comments = [];
        }

        // Get user consents
        userData.consents = await this.getUserConsents(userId);

        // Get recent activities (from audit log)
        const auditLogs = await strapi.documents("plugin::audit-log.audit-log").findMany({
            filters: { userId },
            sort: { createdAt: "desc" },
            limit: 100,
        });
        userData.activities = auditLogs || [];

        // Add export statistics
        userData.statistics = {
            totalRecords:
                (userData.content.polls?.length || 0) +
                (userData.content.votes?.length || 0) +
                (userData.content.comments?.length || 0),
            consentCount: userData.consents.length,
            activityCount: userData.activities.length,
        };

        // Convert to CSV if requested
        if (format === "csv") {
            return this.convertToCSV(userData);
        }

        return userData;
    },

    /**
     * Delete all user data
     */
    async deleteUserData(userId: string) {
        const deletedItems = {
            polls: 0,
            votes: 0,
            comments: 0,
            consents: 0,
            user: false,
        };

        // Delete user's polls (if content type exists)
        try {
            const polls = await strapi.documents("api::poll.poll").findMany({
                filters: { author: userId },
            });

            if (polls && polls.length > 0) {
                for (const poll of polls) {
                    await strapi.documents("api::poll.poll").delete({
                        documentId: poll.documentId,
                    });
                    deletedItems.polls++;
                }
            }
        } catch (error) {
            strapi.log.debug("Poll content type not found, skipping poll deletion");
        }

        // Delete user's votes (if content type exists)
        try {
            const votes = await strapi.documents("api::vote.vote").findMany({
                filters: { user: userId },
            });

            if (votes && votes.length > 0) {
                for (const vote of votes) {
                    await strapi.documents("api::vote.vote").delete({
                        documentId: vote.documentId,
                    });
                    deletedItems.votes++;
                }
            }
        } catch (error) {
            strapi.log.debug("Vote content type not found, skipping vote deletion");
        }

        // Delete user's comments (if exists)
        try {
            const comments = await strapi.documents("api::comment.comment").findMany({
                filters: { author: userId },
            });

            if (comments && comments.length > 0) {
                for (const comment of comments) {
                    await strapi.documents("api::comment.comment").delete({
                        documentId: comment.documentId,
                    });
                    deletedItems.comments++;
                }
            }
        } catch (error) {
            // Comments might not exist
        }

        // Delete user's consents
        const consents = await strapi.documents("plugin::gdpr.consent").findMany({
            filters: { user: userId },
        });

        if (consents && consents.length > 0) {
            for (const consent of consents) {
                await strapi.documents("plugin::gdpr.consent").delete({
                    documentId: consent.documentId,
                });
                deletedItems.consents++;
            }
        }

        // Finally, delete the user account
        const users = await strapi.documents("plugin::users-permissions.user").findMany({
            filters: { id: userId },
            limit: 1,
        });

        if (users && users.length > 0) {
            await strapi.documents("plugin::users-permissions.user").delete({
                documentId: users[0].documentId,
            });
            deletedItems.user = true;
        }

        return { deletedItems };
    },

    /**
     * Check if user can be anonymized
     */
    async canAnonymizeUser(userId: string): Promise<{ allowed: boolean; reason?: string }> {
        const users = await strapi.documents("plugin::users-permissions.user").findMany({
            filters: { id: userId },
            limit: 1,
        });

        const user = users?.[0];

        if (!user) {
            return { allowed: false, reason: "User not found" };
        }

        // Check if user is already anonymized
        if (user.email?.includes("@deleted.local")) {
            return { allowed: false, reason: "User is already anonymized" };
        }

        // Check if user has pending orders or active subscriptions
        // Add your business logic here

        return { allowed: true };
    },

    /**
     * Anonymize user data with transaction support
     */
    async anonymizeUserData(userId: string): Promise<AnonymizationResult> {
        const anonymizedFields = [];
        const anonymousId = crypto.randomBytes(16).toString("hex");
        let filesAnonymized = 0;
        const tablesAffected = new Set<string>();

        // Anonymize user profile
        const users = await strapi.documents("plugin::users-permissions.user").findMany({
            filters: { id: userId },
            limit: 1,
        });

        const user = users?.[0];

        if (!user) {
            throw new Error("User not found");
        }

        await strapi.documents("plugin::users-permissions.user").update({
            documentId: user.documentId,
            data: {
                username: `anonymous_${anonymousId}`,
                email: `anonymous_${anonymousId}@deleted.local`,
                blocked: true,
            } as Record<string, any>,
        });
        anonymizedFields.push("username", "email");

        // Anonymize polls (if content type exists)
        try {
            const polls = await strapi.documents("api::poll.poll").findMany({
                filters: { author: userId },
            });

            if (polls && polls.length > 0) {
                for (const poll of polls) {
                    await strapi.documents("api::poll.poll").update({
                        documentId: poll.documentId,
                        data: {
                            author: null,
                        } as Record<string, any>,
                    });
                }
                anonymizedFields.push("polls.author");
                tablesAffected.add("polls");
            }
        } catch (error) {
            // Poll content type might not exist
            strapi.log.debug("Poll content type not found, skipping poll anonymization");
        }

        // Anonymize comments (if content type exists)
        try {
            const comments = await strapi.documents("api::comment.comment").findMany({
                filters: { author: userId },
            });

            if (comments && comments.length > 0) {
                for (const comment of comments) {
                    await strapi.documents("api::comment.comment").update({
                        documentId: comment.documentId,
                        data: {
                            author: null,
                            content: "[Deleted]",
                        } as Record<string, any>,
                    });
                }
                anonymizedFields.push("comments.author", "comments.content");
                tablesAffected.add("comments");
            }
        } catch (error) {
            // Comment content type might not exist
            strapi.log.debug("Comment content type not found, skipping comment anonymization");
        }

        // Anonymize uploaded files
        try {
            const uploads = await strapi.db.query("plugin::upload.file").findMany({
                where: { related: { $contains: userId } },
            });

            for (const upload of uploads) {
                await strapi.db.query("plugin::upload.file").update({
                    where: { id: upload.id },
                    data: {
                        name: `anonymized_${anonymousId}_${upload.id}`,
                        alternativeText: "[Anonymized]",
                        caption: "[Anonymized]",
                    },
                });
                filesAnonymized++;
            }
            if (uploads.length > 0) {
                anonymizedFields.push("uploads");
                tablesAffected.add("uploads");
            }
        } catch (error) {
            strapi.log.debug("No uploads to anonymize");
        }

        // Anonymize IP addresses in audit logs
        try {
            await strapi.db.query("plugin::audit-log.audit-log").updateMany({
                where: { userId },
                data: {
                    ipAddress: this.anonymizeIP("0.0.0.0"),
                },
            });
            tablesAffected.add("audit-logs");
        } catch (error) {
            strapi.log.debug("No audit logs to anonymize");
        }

        return {
            anonymizedFields,
            statistics: {
                totalFields: anonymizedFields.length,
                tablesAffected: tablesAffected.size,
                filesAnonymized,
            },
        };
    },

    /**
     * Get user consents
     */
    async getUserConsents(userId: string) {
        const consents = await strapi.documents("plugin::gdpr.consent").findMany({
            filters: { user: userId },
            sort: { createdAt: "desc" },
        });

        // Get the latest consent for each type
        const consentMap = new Map();

        if (consents && consents.length > 0) {
            for (const consent of consents) {
                if (!consentMap.has(consent.consentType)) {
                    consentMap.set(consent.consentType, consent);
                }
            }
        }

        return Array.from(consentMap.values());
    },

    /**
     * Update user consents
     */
    async updateUserConsents(userId: string, consents: Array<{ type: string; granted: boolean }>) {
        const updatedConsents = [];

        for (const consent of consents) {
            const newConsent = await strapi.documents("plugin::gdpr.consent").create({
                data: {
                    user: userId,
                    consentType: consent.type,
                    granted: consent.granted,
                    version: "1.0",
                    ipAddress: null, // Should be passed from controller if available
                },
            });
            updatedConsents.push(newConsent);
        }

        return updatedConsents;
    },

    /**
     * Log GDPR-related actions with enhanced metadata
     */
    async logAction(data: {
        action: string;
        userId: string;
        performedBy: string;
        details?: any;
        ipAddress?: string;
        userAgent?: string;
    }) {
        return await strapi.documents("plugin::audit-log.audit-log").create({
            data: {
                action: data.action,
                userId: data.userId,
                performedBy: data.performedBy,
                details: data.details || {},
                timestamp: new Date(),
                ipAddress: data.ipAddress || "unknown",
                userAgent: data.userAgent || "unknown",
            },
        });
    },

    /**
     * Get audit logs with enhanced filtering
     */
    async getAuditLogs(params: {
        userId?: string;
        action?: string;
        startDate?: string;
        endDate?: string;
        search?: string;
        page: number;
        pageSize: number;
    }) {
        const { userId, action, startDate, endDate, search, page, pageSize } = params;

        const filters: any = {};
        if (userId) {
            filters.userId = userId;
        }
        if (action) {
            filters.action = action;
        }
        if (startDate || endDate) {
            filters.timestamp = {};
            if (startDate) {
                filters.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                filters.timestamp.$lte = new Date(endDate);
            }
        }
        if (search) {
            filters.$or = [
                { action: { $contains: search } },
                { "details.format": { $contains: search } },
            ];
        }

        const logs = await strapi.documents("plugin::audit-log.audit-log").findMany({
            filters,
            sort: { timestamp: "desc" },
            limit: pageSize,
            start: (page - 1) * pageSize,
        });

        // Get total count for pagination
        const total = await strapi.db.query("plugin::audit-log.audit-log").count({
            where: filters,
        });

        return {
            data: logs || [],
            meta: {
                pagination: {
                    page,
                    pageSize,
                    total,
                    pageCount: Math.ceil(total / pageSize),
                },
            },
        };
    },

    /**
     * Export audit logs for compliance
     */
    async exportAuditLogs(params: { startDate?: string; endDate?: string; format: string }) {
        const filters: any = {};

        if (params.startDate || params.endDate) {
            filters.timestamp = {};
            if (params.startDate) {
                filters.timestamp.$gte = new Date(params.startDate);
            }
            if (params.endDate) {
                filters.timestamp.$lte = new Date(params.endDate);
            }
        }

        const logs = await strapi.documents("plugin::audit-log.audit-log").findMany({
            filters,
            sort: { timestamp: "desc" },
        });

        if (params.format === "csv") {
            return this.convertAuditLogsToCSV(logs || []);
        }

        return logs || [];
    },

    /**
     * Get GDPR statistics
     */
    async getStatistics() {
        const now = new Date();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Get various counts using database queries for better performance
        const [
            totalExports,
            totalDeletions,
            totalAnonymizations,
            recentExports,
            recentDeletions,
            pendingRequests,
        ] = await Promise.all([
            strapi.db.query("plugin::audit-log.audit-log").count({
                where: { action: "data_export" },
            }),
            strapi.db.query("plugin::audit-log.audit-log").count({
                where: { action: "data_deletion" },
            }),
            strapi.db.query("plugin::audit-log.audit-log").count({
                where: { action: "data_anonymization" },
            }),
            strapi.db.query("plugin::audit-log.audit-log").count({
                where: {
                    action: "data_export",
                    timestamp: { $gte: sevenDaysAgo },
                },
            }),
            strapi.db.query("plugin::audit-log.audit-log").count({
                where: {
                    action: "data_deletion",
                    timestamp: { $gte: sevenDaysAgo },
                },
            }),
            strapi.db.query("plugin::gdpr.gdpr-request").count({
                where: { status: "pending" },
            }),
        ]);

        // Get consent statistics
        const consentStats = await strapi.db.query("plugin::gdpr.consent").count({
            where: { granted: true },
        });

        return {
            overview: {
                totalExports,
                totalDeletions,
                totalAnonymizations,
                pendingRequests,
            },
            recent: {
                exports: recentExports,
                deletions: recentDeletions,
                period: "7_days",
            },
            consents: {
                totalGranted: consentStats,
            },
            compliance: {
                retentionPolicyActive: true,
                encryptionEnabled: true,
                auditLogEnabled: true,
            },
        };
    },

    /**
     * Helper: Convert data to CSV format
     */
    convertToCSV(data: any): string {
        const csvRows: string[] = [];

        // Profile section
        csvRows.push("PROFILE DATA");
        csvRows.push("Field,Value");
        Object.entries(data.profile).forEach(([key, value]) => {
            csvRows.push(`${key},"${value}"`);
        });

        // Content section
        csvRows.push("");
        csvRows.push("CONTENT DATA");
        if (data.content.polls?.length > 0) {
            csvRows.push("Polls");
            csvRows.push("ID,Title,Created At");
            data.content.polls.forEach((poll: any) => {
                csvRows.push(
                    `${poll.documentId || poll.id},"${poll.title || "N/A"}",${poll.createdAt}`,
                );
            });
        }

        // Add more sections as needed

        return csvRows.join("\n");
    },

    /**
     * Helper: Convert audit logs to CSV
     */
    convertAuditLogsToCSV(logs: any[]): string {
        const csvRows: string[] = [];
        csvRows.push("Timestamp,Action,User ID,Performed By,IP Address,Details");

        logs.forEach((log) => {
            const details = JSON.stringify(log.details || {}).replace(/"/g, '""');
            csvRows.push(
                `${log.timestamp},${log.action},${log.userId},${log.performedBy},${log.ipAddress},"${details}"`,
            );
        });

        return csvRows.join("\n");
    },

    /**
     * Helper: Anonymize IP address
     */
    anonymizeIP(ip: string): string {
        if (!ip || ip === "unknown") return "anonymous";

        // IPv4: Remove last octet
        if (ip.includes(".")) {
            const parts = ip.split(".");
            if (parts.length === 4) {
                parts[3] = "0";
                return parts.join(".");
            }
        }

        // IPv6: Remove last 64 bits
        if (ip.includes(":")) {
            const parts = ip.split(":");
            if (parts.length >= 4) {
                return parts.slice(0, 4).join(":") + "::";
            }
        }

        return "anonymous";
    },
});

export default service;
