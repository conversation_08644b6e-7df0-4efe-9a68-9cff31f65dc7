import React from "react";
import { Box, Typography } from "@strapi/design-system";

const HomePage = () => {
    return (
        <Box padding={8}>
            <Box paddingBottom={6}>
                <Typography variant="alpha" as="h1">
                    GDPR Compliance
                </Typography>
                <Typography variant="omega" textColor="neutral600">
                    Manage data privacy, user requests, and compliance settings
                </Typography>
            </Box>

            <Box padding={8} background="neutral0" hasRadius shadow="tableShadow">
                <Typography variant="beta" as="h2">
                    GDPR Compliance Plugin
                </Typography>
                <Typography variant="epsilon">
                    This plugin helps manage data privacy, user requests, and compliance settings
                    according to GDPR regulations.
                </Typography>

                <Box paddingTop={6}>
                    <Typography variant="gamma" as="h3">
                        Features
                    </Typography>
                    <Box paddingTop={2}>
                        <Typography variant="omega">• Data export requests management</Typography>
                        <Typography variant="omega">• Data deletion and anonymization</Typography>
                        <Typography variant="omega">• Consent tracking and management</Typography>
                        <Typography variant="omega">• Audit logging for compliance</Typography>
                    </Box>
                </Box>
            </Box>
        </Box>
    );
};

export default HomePage;
