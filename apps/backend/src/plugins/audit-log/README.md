# Audit Log Plugin

A comprehensive audit logging plugin for Strapi that tracks all important system events with search capabilities and retention policies.

## Features

- **Comprehensive Event Tracking**: Logs user authentication, content operations, permission changes, system configuration changes, and GDPR operations
- **Search and Filtering**: Advanced search capabilities with filters by event type, action, target, user, and date range
- **Retention Policies**: Automatic cleanup of old logs based on configurable retention period
- **Admin-Only Access**: All endpoints are protected and accessible only to administrators
- **Export Functionality**: Export audit logs in JSON or CSV format
- **Statistics Dashboard**: View event counts, action distributions, and most active users

## Installation

The plugin is already installed as a local plugin in the Strapi project.

## Configuration

Configure the plugin in your Strapi configuration:

```javascript
// config/plugins.js
module.exports = {
    "audit-log": {
        enabled: true,
        config: {
            retentionDays: 90, // Keep logs for 90 days
            enabledEvents: [
                "user.login",
                "user.logout",
                "content.create",
                "content.update",
                "content.delete",
                "permission.change",
                "config.change",
                "gdpr.operation",
            ],
            maxLogsPerPage: 100,
        },
    },
};
```

## API Endpoints

All endpoints require admin authentication.

### Search Logs

```
GET /audit-log/logs?event=user.login&startDate=2024-01-01&page=1&pageSize=25
```

### Get Statistics

```
GET /audit-log/logs/statistics?startDate=2024-01-01&endDate=2024-12-31
```

### Export Logs

```
GET /audit-log/logs/export?format=csv&startDate=2024-01-01
```

### Get Event Types

```
GET /audit-log/event-types
```

### Get Actions

```
GET /audit-log/actions
```

### Enforce Retention Policy

```
POST /audit-log/retention/enforce
```

### Get Configuration

```
GET /audit-log/config
```

## Event Types

The plugin tracks the following events by default:

- `user.login` - User authentication
- `user.logout` - User logout
- `content.create` - Content creation
- `content.update` - Content modification
- `content.delete` - Content deletion
- `permission.change` - Permission updates
- `config.change` - System configuration changes
- `gdpr.operation` - GDPR-related operations (from GDPR plugin)
- `audit.export` - Audit log exports
- `audit.retention` - Retention policy enforcement

## Retention Policy

The plugin automatically enforces retention policies:

- Runs daily to remove logs older than the configured retention period
- Default retention period is 90 days
- Can be manually triggered via the API
- Logs the retention enforcement action itself

## Security

- All endpoints require admin authentication
- Sensitive operations are logged (exports, retention enforcement)
- User information is captured for accountability
- IP addresses and user agents are recorded when available

## Integration with GDPR Plugin

The audit log plugin listens for GDPR events emitted by the GDPR plugin:

```javascript
// GDPR plugin emits:
strapi.ee.emit("gdpr.operation", {
    action: "export",
    userId: 123,
    performedBy: 456,
    operation: "data-export",
    reason: "User request",
    details: { format: "json" },
});
```

These events are automatically captured and logged.

## Database Schema

The plugin creates an `audit_logs` table with the following fields:

- `event` (string, indexed) - Event type
- `action` (enum, indexed) - Action performed
- `targetType` (string, indexed) - Type of target entity
- `targetId` (string, indexed) - ID of target entity
- `userId` (integer, indexed) - User who performed the action
- `userEmail` (string, indexed) - Email of the user
- `ip` (string) - IP address
- `userAgent` (text) - User agent string
- `metadata` (json) - Additional event data
- `timestamp` (datetime, indexed) - When the event occurred

## Performance Considerations

- All search fields are indexed for fast queries
- Pagination is enforced with configurable limits
- Retention policy prevents unbounded growth
- Asynchronous logging to minimize impact on operations
