export default async ({ strapi }: { strapi: any }) => {
    // Set up lifecycle hooks for content type events
    strapi.db.lifecycles.subscribe({
        models: ["*"],

        async afterCreate(event) {
            const { model, result } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.create",
                    action: "create",
                    targetType: model.uid,
                    targetId: result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        data: result,
                    },
                });
        },

        async afterUpdate(event) {
            const { model, result, params } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.update",
                    action: "update",
                    targetType: model.uid,
                    targetId: result.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        data: result,
                        where: params.where,
                    },
                });
        },

        async afterDelete(event) {
            const { model, result, params } = event;
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "content.delete",
                    action: "delete",
                    targetType: model.uid,
                    targetId: params.where?.id || result?.id,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        modelName: model.singularName,
                        deletedData: result,
                        where: params.where,
                    },
                });
        },
    });

    // Set up authentication event listeners (only available in Enterprise Edition)
    if (strapi.ee && typeof strapi.ee.on === "function") {
        strapi.ee.on("admin.auth.success", async (data) => {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "user.login",
                    action: "login",
                    targetType: "admin::user",
                    targetId: data.user.id,
                    userId: data.user.id,
                    metadata: {
                        email: data.user.email,
                        ip: data.ip,
                        userAgent: data.userAgent,
                    },
                });
        });

        strapi.ee.on("admin.logout", async (data) => {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "user.logout",
                    action: "logout",
                    targetType: "admin::user",
                    targetId: data.userId,
                    userId: data.userId,
                    metadata: {
                        ip: data.ip,
                        userAgent: data.userAgent,
                    },
                });
        });

        // Set up permission change listeners
        strapi.ee.on("permission.update", async (data) => {
            await strapi.plugin("audit-log").service("auditLog").log({
                event: "permission.change",
                action: "update",
                targetType: "admin::permission",
                targetId: data.permissionId,
                userId: strapi.requestContext?.get()?.state?.user?.id,
                metadata: data,
            });
        });

        // Set up configuration change listeners
        strapi.ee.on("config.update", async (data) => {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "config.change",
                    action: "update",
                    targetType: "core::settings",
                    targetId: data.key,
                    userId: strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        key: data.key,
                        previousValue: data.previousValue,
                        newValue: data.newValue,
                    },
                });
        });

        // Listen for GDPR operations from the GDPR plugin
        strapi.ee.on("gdpr.operation", async (data) => {
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "gdpr.operation",
                    action: data.action,
                    targetType: "plugin::users-permissions.user",
                    targetId: data.userId,
                    userId: data.performedBy || strapi.requestContext?.get()?.state?.user?.id,
                    metadata: {
                        operation: data.operation,
                        reason: data.reason,
                        details: data.details,
                    },
                });
        });
    } else {
        strapi.log.info(
            "Enterprise Edition events not available - some audit logging features will be limited",
        );
    }

    // Schedule retention policy enforcement
    const runRetentionPolicy = async () => {
        try {
            await strapi.plugin("audit-log").service("auditLog").enforceRetentionPolicy();
        } catch (error) {
            strapi.log.error("Failed to enforce audit log retention policy:", error);
        }
    };

    // Run retention policy daily
    setInterval(runRetentionPolicy, 24 * 60 * 60 * 1000);

    // Run retention policy on startup
    setTimeout(runRetentionPolicy, 5000);
};
