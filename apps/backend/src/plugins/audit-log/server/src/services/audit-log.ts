export interface AuditLogEntry {
    event: string;
    action: string;
    targetType: string;
    targetId?: string;
    userId?: number;
    metadata?: any;
    ip?: string;
    userAgent?: string;
}

export interface SearchParams {
    event?: string;
    action?: string;
    targetType?: string;
    targetId?: string;
    userId?: number;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
}

export default ({ strapi }: { strapi: any }) => ({
    /**
     * Log an audit event
     */
    async log(entry: AuditLogEntry): Promise<any> {
        try {
            const config = strapi.plugin("audit-log").config;

            // Check if this event type is enabled
            if (config.enabledEvents && !config.enabledEvents.includes(entry.event)) {
                return null;
            }

            // Get request context if available
            const ctx = strapi.requestContext?.get();

            // Enrich entry with user information
            let userEmail;
            if (entry.userId) {
                const user = await strapi.query("admin::user").findOne({
                    where: { id: entry.userId },
                    select: ["email"],
                });
                userEmail = user?.email;
            }

            // Create the audit log entry
            const auditEntry = await strapi.query("plugin::audit-log.audit-log").create({
                data: {
                    event: entry.event,
                    action: entry.action,
                    targetType: entry.targetType,
                    targetId: entry.targetId?.toString(),
                    userId: entry.userId,
                    userEmail,
                    ip: entry.ip || ctx?.request?.ip || ctx?.ip,
                    userAgent: entry.userAgent || ctx?.request?.headers?.["user-agent"],
                    metadata: entry.metadata,
                    timestamp: new Date(),
                },
            });

            strapi.log.info(`Audit log created: ${entry.event} - ${entry.action}`);
            return auditEntry;
        } catch (error) {
            strapi.log.error("Failed to create audit log entry:", error);
            throw error;
        }
    },

    /**
     * Search audit logs with filters
     */
    async search(params: SearchParams): Promise<{ data: any[]; meta: any }> {
        const {
            event,
            action,
            targetType,
            targetId,
            userId,
            startDate,
            endDate,
            page = 1,
            pageSize = 25,
        } = params;

        const config = strapi.plugin("audit-log").config;
        const maxPageSize = config.maxLogsPerPage || 100;
        const limit = Math.min(pageSize, maxPageSize);
        const offset = (page - 1) * limit;

        // Build query filters
        const filters: any = {};

        if (event) filters.event = event;
        if (action) filters.action = action;
        if (targetType) filters.targetType = targetType;
        if (targetId) filters.targetId = targetId;
        if (userId) filters.userId = userId;

        if (startDate || endDate) {
            filters.timestamp = {};
            if (startDate) filters.timestamp.$gte = new Date(startDate);
            if (endDate) filters.timestamp.$lte = new Date(endDate);
        }

        // Execute query
        const [entries, count] = await Promise.all([
            strapi.query("plugin::audit-log.audit-log").findMany({
                where: filters,
                orderBy: { timestamp: "desc" },
                limit,
                offset,
            }),
            strapi.query("plugin::audit-log.audit-log").count({ where: filters }),
        ]);

        return {
            data: entries,
            meta: {
                pagination: {
                    page,
                    pageSize: limit,
                    pageCount: Math.ceil(count / limit),
                    total: count,
                },
            },
        };
    },

    /**
     * Get audit log statistics
     */
    async getStatistics(params: { startDate?: string; endDate?: string }): Promise<any> {
        const filters: any = {};

        if (params.startDate || params.endDate) {
            filters.timestamp = {};
            if (params.startDate) filters.timestamp.$gte = new Date(params.startDate);
            if (params.endDate) filters.timestamp.$lte = new Date(params.endDate);
        }

        // Get event counts by type
        const eventCounts = await strapi.db.connection.raw(
            `
      SELECT event, COUNT(*) as count
      FROM audit_logs
      WHERE ${params.startDate ? "timestamp >= ?" : "1=1"}
        AND ${params.endDate ? "timestamp <= ?" : "1=1"}
      GROUP BY event
      ORDER BY count DESC
    `,
            [
                ...(params.startDate ? [params.startDate] : []),
                ...(params.endDate ? [params.endDate] : []),
            ],
        );

        // Get action counts
        const actionCounts = await strapi.db.connection.raw(
            `
      SELECT action, COUNT(*) as count
      FROM audit_logs
      WHERE ${params.startDate ? "timestamp >= ?" : "1=1"}
        AND ${params.endDate ? "timestamp <= ?" : "1=1"}
      GROUP BY action
      ORDER BY count DESC
    `,
            [
                ...(params.startDate ? [params.startDate] : []),
                ...(params.endDate ? [params.endDate] : []),
            ],
        );

        // Get most active users
        const userActivity = await strapi.db.connection.raw(
            `
      SELECT userId, userEmail, COUNT(*) as count
      FROM audit_logs
      WHERE userId IS NOT NULL
        AND ${params.startDate ? "timestamp >= ?" : "1=1"}
        AND ${params.endDate ? "timestamp <= ?" : "1=1"}
      GROUP BY userId, userEmail
      ORDER BY count DESC
      LIMIT 10
    `,
            [
                ...(params.startDate ? [params.startDate] : []),
                ...(params.endDate ? [params.endDate] : []),
            ],
        );

        return {
            eventCounts: eventCounts.rows || eventCounts,
            actionCounts: actionCounts.rows || actionCounts,
            mostActiveUsers: userActivity.rows || userActivity,
        };
    },

    /**
     * Export audit logs
     */
    async export(params: SearchParams, format: "json" | "csv" = "json"): Promise<string> {
        // Remove pagination for export
        const allParams = { ...params, page: 1, pageSize: 10000 };
        const { data } = await this.search(allParams);

        if (format === "csv") {
            // CSV header
            const headers = [
                "Timestamp",
                "Event",
                "Action",
                "Target Type",
                "Target ID",
                "User ID",
                "User Email",
                "IP",
                "User Agent",
            ];
            const csvRows = [headers.join(",")];

            // CSV data rows
            for (const entry of data) {
                const row = [
                    entry.timestamp,
                    entry.event,
                    entry.action,
                    entry.targetType,
                    entry.targetId || "",
                    entry.userId || "",
                    entry.userEmail || "",
                    entry.ip || "",
                    (entry.userAgent || "").replace(/,/g, ";"), // Replace commas in user agent
                ];
                csvRows.push(row.map((val) => `"${val}"`).join(","));
            }

            return csvRows.join("\n");
        }

        // Default to JSON
        return JSON.stringify(data, null, 2);
    },

    /**
     * Enforce retention policy
     */
    async enforceRetentionPolicy(): Promise<number> {
        const config = strapi.plugin("audit-log").config;
        const retentionDays = config.retentionDays || 90;

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        strapi.log.info(
            `Enforcing audit log retention policy: removing logs older than ${cutoffDate.toISOString()}`,
        );

        const result = await strapi.query("plugin::audit-log.audit-log").deleteMany({
            where: {
                timestamp: {
                    $lt: cutoffDate,
                },
            },
        });

        const deletedCount = result.count || 0;
        strapi.log.info(
            `Removed ${deletedCount} audit log entries older than ${retentionDays} days`,
        );

        return deletedCount;
    },

    /**
     * Get available event types
     */
    async getEventTypes(): Promise<string[]> {
        const config = strapi.plugin("audit-log").config;
        return config.enabledEvents || [];
    },

    /**
     * Get available actions
     */
    getActions(): string[] {
        return [
            "create",
            "read",
            "update",
            "delete",
            "login",
            "logout",
            "export",
            "anonymize",
            "other",
        ];
    },
});
