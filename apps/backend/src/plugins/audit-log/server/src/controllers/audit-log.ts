export default ({ strapi }: { strapi: any }) => ({
    /**
     * Search audit logs
     */
    async search(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to access audit logs");
            }

            const {
                event,
                action,
                targetType,
                targetId,
                userId,
                startDate,
                endDate,
                page = 1,
                pageSize = 25,
            } = ctx.query;

            const result = await strapi
                .plugin("audit-log")
                .service("auditLog")
                .search({
                    event,
                    action,
                    targetType,
                    targetId,
                    userId: userId ? parseInt(userId) : undefined,
                    startDate,
                    endDate,
                    page: parseInt(page),
                    pageSize: parseInt(pageSize),
                });

            ctx.body = result;
        } catch (error) {
            strapi.log.error("Audit log search error:", error);
            ctx.throw(500, "Failed to search audit logs");
        }
    },

    /**
     * Get audit log statistics
     */
    async statistics(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to access audit logs");
            }

            const { startDate, endDate } = ctx.query;

            const stats = await strapi
                .plugin("audit-log")
                .service("auditLog")
                .getStatistics({ startDate, endDate });

            ctx.body = stats;
        } catch (error) {
            strapi.log.error("Audit log statistics error:", error);
            ctx.throw(500, "Failed to get audit log statistics");
        }
    },

    /**
     * Export audit logs
     */
    async export(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to export audit logs");
            }

            const {
                event,
                action,
                targetType,
                targetId,
                userId,
                startDate,
                endDate,
                format = "json",
            } = ctx.query;

            // Log the export action itself
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "audit.export",
                    action: "export",
                    targetType: "plugin::audit-log.audit-log",
                    userId: ctx.state.user.id,
                    metadata: {
                        filters: {
                            event,
                            action,
                            targetType,
                            targetId,
                            userId,
                            startDate,
                            endDate,
                        },
                        format,
                    },
                });

            const data = await strapi
                .plugin("audit-log")
                .service("auditLog")
                .export(
                    {
                        event,
                        action,
                        targetType,
                        targetId,
                        userId: userId ? parseInt(userId) : undefined,
                        startDate,
                        endDate,
                    },
                    format as "json" | "csv",
                );

            // Set appropriate headers
            if (format === "csv") {
                ctx.set("Content-Type", "text/csv");
                ctx.set(
                    "Content-Disposition",
                    `attachment; filename="audit-logs-${new Date().toISOString().split("T")[0]}.csv"`,
                );
            } else {
                ctx.set("Content-Type", "application/json");
                ctx.set(
                    "Content-Disposition",
                    `attachment; filename="audit-logs-${new Date().toISOString().split("T")[0]}.json"`,
                );
            }

            ctx.body = data;
        } catch (error) {
            strapi.log.error("Audit log export error:", error);
            ctx.throw(500, "Failed to export audit logs");
        }
    },

    /**
     * Get available event types
     */
    async eventTypes(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to access audit logs");
            }

            const eventTypes = await strapi.plugin("audit-log").service("auditLog").getEventTypes();

            ctx.body = { eventTypes };
        } catch (error) {
            strapi.log.error("Get event types error:", error);
            ctx.throw(500, "Failed to get event types");
        }
    },

    /**
     * Get available actions
     */
    async actions(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to access audit logs");
            }

            const actions = strapi.plugin("audit-log").service("auditLog").getActions();

            ctx.body = { actions };
        } catch (error) {
            strapi.log.error("Get actions error:", error);
            ctx.throw(500, "Failed to get actions");
        }
    },

    /**
     * Manually trigger retention policy
     */
    async enforceRetention(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to manage audit logs");
            }

            // Log the retention enforcement action
            await strapi
                .plugin("audit-log")
                .service("auditLog")
                .log({
                    event: "audit.retention",
                    action: "delete",
                    targetType: "plugin::audit-log.audit-log",
                    userId: ctx.state.user.id,
                    metadata: {
                        triggeredBy: "manual",
                    },
                });

            const deletedCount = await strapi
                .plugin("audit-log")
                .service("auditLog")
                .enforceRetentionPolicy();

            ctx.body = {
                message: "Retention policy enforced successfully",
                deletedCount,
            };
        } catch (error) {
            strapi.log.error("Enforce retention error:", error);
            ctx.throw(500, "Failed to enforce retention policy");
        }
    },

    /**
     * Get plugin configuration
     */
    async config(ctx) {
        try {
            // Check admin permissions
            if (!ctx.state.user || !ctx.state.user.isActive) {
                return ctx.unauthorized("You must be an admin to access audit logs");
            }

            const config = strapi.plugin("audit-log").config;

            ctx.body = {
                retentionDays: config.retentionDays,
                enabledEvents: config.enabledEvents,
                maxLogsPerPage: config.maxLogsPerPage,
            };
        } catch (error) {
            strapi.log.error("Get config error:", error);
            ctx.throw(500, "Failed to get audit log configuration");
        }
    },
});
