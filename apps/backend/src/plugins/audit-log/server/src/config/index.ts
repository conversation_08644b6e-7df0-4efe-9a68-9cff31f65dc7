export default {
    default: {
        retentionDays: 90,
        enabledEvents: [
            "user.login",
            "user.logout",
            "content.create",
            "content.update",
            "content.delete",
            "permission.change",
            "config.change",
            "gdpr.operation",
        ],
        maxLogsPerPage: 100,
    },
    validator: (config: any) => {
        if (config.retentionDays && config.retentionDays < 1) {
            throw new Error("retentionDays must be at least 1");
        }
    },
};
