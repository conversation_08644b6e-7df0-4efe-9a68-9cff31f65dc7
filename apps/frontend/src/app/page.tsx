export default function Home() {
    return (
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
            {/* Hero Section */}
            <section className="relative py-20 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center">
                        <h1 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-6">
                            Bienvenue sur <span className="text-blue-600">CivicPoll</span>
                        </h1>
                        <p className="text-xl sm:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
                            La plateforme de sondages géolocalisés pour la France. Exprimez votre
                            opinion et participez à la vie démocratique de votre région.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
                                Découvrir les sondages
                            </button>
                            <button className="border border-blue-600 text-blue-600 hover:bg-blue-50 font-semibold py-3 px-8 rounded-lg transition-colors">
                                En savoir plus
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                            Pourquoi choisir CivicPoll ?
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Une plateforme moderne, sécurisée et conforme RGPD pour recueillir
                            l'opinion des citoyens français.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-blue-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                    />
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                Géolocalisé
                            </h3>
                            <p className="text-gray-600">
                                Participez uniquement aux sondages de votre région, ville ou
                                département pour des résultats pertinents.
                            </p>
                        </div>

                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-green-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Sécurisé</h3>
                            <p className="text-gray-600">
                                Vos données sont protégées avec un chiffrement de niveau bancaire et
                                une conformité RGPD totale.
                            </p>
                        </div>

                        <div className="text-center p-6">
                            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    className="w-8 h-8 text-purple-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13 10V3L4 14h7v7l9-11h-7z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Rapide</h3>
                            <p className="text-gray-600">
                                Interface moderne et intuitive pour participer aux sondages en
                                quelques clics seulement.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Status Section */}
            <section className="py-16 bg-gray-50">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-8">
                        <h3 className="text-2xl font-semibold text-blue-900 mb-4">
                            🚧 Phase de Préparation
                        </h3>
                        <p className="text-blue-800 mb-6">
                            CivicPoll est actuellement en phase de développement. Notre équipe
                            travaille sur la mise en place d'une infrastructure sécurisée et
                            conforme RGPD pour vous offrir la meilleure expérience possible.
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center justify-center">
                                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                Infrastructure sécurisée
                            </div>
                            <div className="flex items-center justify-center">
                                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                Conformité RGPD
                            </div>
                            <div className="flex items-center justify-center">
                                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                Tests de sécurité
                            </div>
                            <div className="flex items-center justify-center">
                                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                                Interface utilisateur
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}
