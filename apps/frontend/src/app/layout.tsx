import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
    subsets: ["latin"],
    variable: "--font-inter",
});

export const metadata: Metadata = {
    title: "CivicPoll - Plateforme de Sondages SMATFLOW",
    description:
        "CivicPoll est la plateforme de sondages géolocalisés de SMATFLOW pour la France. Participez aux sondages de votre région et exprimez votre opinion.",
    keywords: ["sondages", "opinion", "france", "géolocalisation", "participation", "citoyenne"],
    authors: [{ name: "SMATFLOW" }],
    creator: "SMATFLOW",
    publisher: "SMATFLOW",
    robots: "index, follow",
    openGraph: {
        title: "CivicPoll - Plateforme de Sondages SMATFLOW",
        description: "Participez aux sondages géolocalisés de votre région avec CivicPoll",
        url: "https://civicpoll.fr.smatflow.xyz",
        siteName: "CivicPoll",
        locale: "fr_FR",
        type: "website",
    },
    twitter: {
        card: "summary_large_image",
        title: "CivicPoll - Plateforme de Sondages SMATFLOW",
        description: "Participez aux sondages géolocalisés de votre région",
    },
    viewport: "width=device-width, initial-scale=1",
    themeColor: "#1f2937",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="fr" className={inter.variable}>
            <head>
                <link rel="icon" href="/favicon.ico" />
                <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
                <meta name="msapplication-TileColor" content="#1f2937" />
                <meta name="theme-color" content="#1f2937" />
            </head>
            <body className="font-inter antialiased bg-gray-50 text-gray-900">
                <div className="min-h-screen flex flex-col">
                    <header className="bg-white shadow-sm border-b border-gray-200">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center h-16">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <h1 className="text-2xl font-bold text-gray-900">
                                            <span className="text-blue-600">Civic</span>Poll
                                        </h1>
                                        <p className="text-xs text-gray-500 mt-1">by SMATFLOW</p>
                                    </div>
                                </div>
                                <nav className="hidden md:block">
                                    <div className="ml-10 flex items-baseline space-x-4">
                                        <a
                                            href="/"
                                            className="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
                                        >
                                            Accueil
                                        </a>
                                        <a
                                            href="/sondages"
                                            className="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
                                        >
                                            Sondages
                                        </a>
                                        <a
                                            href="/mon-compte"
                                            className="text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
                                        >
                                            Mon Compte
                                        </a>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </header>

                    <main className="flex-1">{children}</main>

                    <footer className="bg-gray-800 text-white">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">CivicPoll</h3>
                                    <p className="text-gray-300 text-sm">
                                        La plateforme de sondages géolocalisés pour la France.
                                        Exprimez votre opinion et participez à la vie démocratique.
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">Liens utiles</h3>
                                    <ul className="space-y-2 text-sm">
                                        <li>
                                            <a
                                                href="/confidentialite"
                                                className="text-gray-300 hover:text-white"
                                            >
                                                Politique de confidentialité
                                            </a>
                                        </li>
                                        <li>
                                            <a
                                                href="/conditions"
                                                className="text-gray-300 hover:text-white"
                                            >
                                                Conditions d'utilisation
                                            </a>
                                        </li>
                                        <li>
                                            <a
                                                href="/rgpd"
                                                className="text-gray-300 hover:text-white"
                                            >
                                                Vos données RGPD
                                            </a>
                                        </li>
                                        <li>
                                            <a
                                                href="/contact"
                                                className="text-gray-300 hover:text-white"
                                            >
                                                Contact
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">SMATFLOW</h3>
                                    <p className="text-gray-300 text-sm">
                                        CivicPoll est développé par SMATFLOW, votre partenaire pour
                                        l'innovation numérique.
                                    </p>
                                    <div className="mt-4">
                                        <p className="text-xs text-gray-400">
                                            © 2024 SMATFLOW. Tous droits réservés.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            </body>
        </html>
    );
}
