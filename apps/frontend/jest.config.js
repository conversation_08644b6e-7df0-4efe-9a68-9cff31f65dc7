const baseConfig = require("../../jest.config.base");

/** @type {import('jest').Config} */
module.exports = {
    ...baseConfig,
    displayName: "frontend",
    testEnvironment: "jsdom",
    setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
    moduleNameMapper: {
        ...baseConfig.moduleNameMapper,
        "^@/(.*)$": "<rootDir>/src/$1",
        "\\.(css|less|scss|sass)$": "identity-obj-proxy",
        "\\.(jpg|jpeg|png|gif|webp|svg)$": "<rootDir>/__mocks__/fileMock.js",
    },
    transform: {
        "^.+\\.(ts|tsx)$": [
            "ts-jest",
            {
                tsconfig: {
                    jsx: "react",
                },
            },
        ],
    },
    testPathIgnorePatterns: ["/node_modules/", "/.next/"],
    transformIgnorePatterns: ["/node_modules/", "^.+\\.module\\.(css|sass|scss)$"],
    coveragePathIgnorePatterns: ["/node_modules/", "/.next/", "/coverage/"],
};
