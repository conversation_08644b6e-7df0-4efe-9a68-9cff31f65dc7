import type { NextConfig } from "next";

const securityHeaders = [
    {
        key: "X-DNS-Prefetch-Control",
        value: "on",
    },
    {
        key: "Strict-Transport-Security",
        value: "max-age=63072000; includeSubDomains; preload",
    },
    {
        key: "X-XSS-Protection",
        value: "1; mode=block",
    },
    {
        key: "X-Frame-Options",
        value: "SAMEORIGIN",
    },
    {
        key: "X-Content-Type-Options",
        value: "nosniff",
    },
    {
        key: "Referrer-Policy",
        value: "origin-when-cross-origin",
    },
];

const nextConfig: NextConfig = {
    // Security headers
    async headers() {
        return [
            {
                // Apply these headers to all routes in your application
                source: "/:path*",
                headers: securityHeaders,
            },
        ];
    },

    // Enable strict mode for better React development
    reactStrictMode: true,

    // Optimize images
    images: {
        domains: ["localhost"],
    },

    // Environment variables validation
    env: {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:1337",
        NEXT_PUBLIC_FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL || "http://localhost:3000",
    },
};

export default nextConfig;
