{"$schema": "https://turbo.build/schema.json", "ui": "stream", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "cache": true}, "test:watch": {"cache": false, "persistent": true}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "cache": true}}}