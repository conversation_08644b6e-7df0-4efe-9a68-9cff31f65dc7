# Phase 2 - Implémentation Technique : Enrichissement et API

## Vue d'ensemble

Cette phase enrichit considérablement l'expérience avec une bibliothèque de templates étendue, 20 types de questions différents, une API publique complète et des notifications multi-canal.

## 1. Content-Types pour l'Enrichissement

### 1.1 Template (Modèles de Sondages)

```javascript
// api/template/content-types/template/schema.json
{
  "kind": "collectionType",
  "collectionName": "templates",
  "info": {
    "singularName": "template",
    "pluralName": "templates",
    "displayName": "Template Sondage"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "description": {
      "type": "text",
      "required": true
    },
    "category": {
      "type": "enumeration",
      "enum": ["rh", "marketing", "politique", "evenementiel", "satisfaction", "education", "sante", "custom"],
      "required": true
    },
    "methodology": {
      "type": "enumeration",
      "enum": ["nps", "csat", "enps", "ces", "likert", "custom"],
      "default": "custom"
    },
    "icon": {
      "type": "media",
      "allowedTypes": ["images"]
    },
    "questions_structure": {
      "type": "json",
      "required": true,
      "description": "Structure JSON des questions du template"
    },
    "settings": {
      "type": "json",
      "default": {
        "estimated_time": 5,
        "recommended_sample_size": 100,
        "best_practices": []
      }
    },
    "tags": {
      "type": "json",
      "description": "Tags pour recherche et filtrage"
    },
    "is_public": {
      "type": "boolean",
      "default": true
    },
    "created_by_organization": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::organization.organization"
    },
    "usage_count": {
      "type": "integer",
      "default": 0
    }
  }
}
```

### 1.2 QuestionType (20 Types de Questions)

```javascript
// api/question-type/content-types/question-type/schema.json
{
  "kind": "collectionType",
  "collectionName": "question_types",
  "info": {
    "singularName": "question-type",
    "pluralName": "question-types",
    "displayName": "Type de Question"
  },
  "attributes": {
    "identifier": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "name": {
      "type": "string",
      "required": true
    },
    "description": {
      "type": "text"
    },
    "category": {
      "type": "enumeration",
      "enum": ["choice", "text", "rating", "ranking", "media", "advanced"],
      "required": true
    },
    "icon": {
      "type": "string"
    },
    "validation_schema": {
      "type": "json",
      "required": true,
      "description": "JSON Schema pour validation"
    },
    "default_settings": {
      "type": "json"
    },
    "example": {
      "type": "json"
    },
    "requires_plugin": {
      "type": "string",
      "description": "Plugin requis (ex: media-upload)"
    }
  }
}
```

### 1.3 Notification

```javascript
// api/notification/content-types/notification/schema.json
{
  "kind": "collectionType",
  "collectionName": "notifications",
  "info": {
    "singularName": "notification",
    "pluralName": "notifications",
    "displayName": "Notification"
  },
  "attributes": {
    "type": {
      "type": "enumeration",
      "enum": ["poll_invitation", "poll_reminder", "poll_closed", "results_available", "custom"],
      "required": true
    },
    "channel": {
      "type": "enumeration",
      "enum": ["email", "sms", "in_app", "push"],
      "required": true
    },
    "recipient": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.user"
    },
    "subject": {
      "type": "string"
    },
    "content": {
      "type": "richtext",
      "required": true
    },
    "template_id": {
      "type": "string",
      "description": "ID du template de notification"
    },
    "variables": {
      "type": "json",
      "description": "Variables pour le template"
    },
    "status": {
      "type": "enumeration",
      "enum": ["pending", "sent", "delivered", "failed", "bounced"],
      "default": "pending"
    },
    "scheduled_at": {
      "type": "datetime"
    },
    "sent_at": {
      "type": "datetime"
    },
    "poll": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::poll.poll"
    },
    "metadata": {
      "type": "json",
      "description": "Provider response, message ID, etc."
    }
  }
}
```

### 1.4 API Configuration

```javascript
// api/api-key/content-types/api-key/schema.json
{
  "kind": "collectionType",
  "collectionName": "api_keys",
  "info": {
    "singularName": "api-key",
    "pluralName": "api-keys",
    "displayName": "Clé API"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true
    },
    "key": {
      "type": "string",
      "required": true,
      "unique": true,
      "private": true
    },
    "organization": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::organization.organization"
    },
    "permissions": {
      "type": "json",
      "default": ["read:polls", "create:polls"],
      "description": "Scopes autorisés"
    },
    "rate_limit": {
      "type": "integer",
      "default": 1000,
      "description": "Requêtes par heure"
    },
    "last_used_at": {
      "type": "datetime"
    },
    "expires_at": {
      "type": "datetime"
    },
    "active": {
      "type": "boolean",
      "default": true
    },
    "usage_stats": {
      "type": "json",
      "default": {
        "total_requests": 0,
        "monthly_requests": {}
      }
    }
  }
}
```

### 1.5 Webhook

```javascript
// api/webhook/content-types/webhook/schema.json
{
  "kind": "collectionType",
  "collectionName": "webhooks",
  "info": {
    "singularName": "webhook",
    "pluralName": "webhooks",
    "displayName": "Webhook"
  },
  "attributes": {
    "url": {
      "type": "string",
      "required": true,
      "regex": "^https://"
    },
    "events": {
      "type": "json",
      "required": true,
      "default": ["poll.created", "response.received"]
    },
    "organization": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::organization.organization"
    },
    "secret": {
      "type": "string",
      "private": true,
      "description": "Secret pour signature HMAC"
    },
    "active": {
      "type": "boolean",
      "default": true
    },
    "retry_policy": {
      "type": "json",
      "default": {
        "max_retries": 3,
        "backoff_seconds": [5, 30, 120]
      }
    },
    "last_triggered_at": {
      "type": "datetime"
    },
    "failure_count": {
      "type": "integer",
      "default": 0
    }
  }
}
```

## 2. Les 20 Types de Questions

### 2.1 Implémentation des Types

```javascript
// src/api/question-types/data/types.js
const questionTypes = [
    // === CHOIX ===
    {
        identifier: "single_choice",
        name: "Choix unique",
        category: "choice",
        validation_schema: {
            type: "string",
            enum: [], // Dynamique selon options
        },
    },
    {
        identifier: "multiple_choice",
        name: "Choix multiple",
        category: "choice",
        validation_schema: {
            type: "array",
            items: { type: "string" },
            minItems: 1,
        },
    },
    {
        identifier: "dropdown",
        name: "Liste déroulante",
        category: "choice",
        validation_schema: {
            type: "string",
            enum: [],
        },
    },

    // === TEXTE ===
    {
        identifier: "short_text",
        name: "Texte court",
        category: "text",
        validation_schema: {
            type: "string",
            maxLength: 255,
        },
    },
    {
        identifier: "long_text",
        name: "Texte long",
        category: "text",
        validation_schema: {
            type: "string",
            maxLength: 5000,
        },
    },
    {
        identifier: "email",
        name: "Email",
        category: "text",
        validation_schema: {
            type: "string",
            format: "email",
        },
    },
    {
        identifier: "phone",
        name: "Téléphone",
        category: "text",
        validation_schema: {
            type: "string",
            pattern: "^[+]?[0-9\\s-]+$",
        },
    },
    {
        identifier: "number",
        name: "Nombre",
        category: "text",
        validation_schema: {
            type: "number",
            minimum: 0,
            maximum: 999999,
        },
    },

    // === NOTATION ===
    {
        identifier: "likert_scale",
        name: "Échelle de Likert",
        category: "rating",
        validation_schema: {
            type: "integer",
            minimum: 1,
            maximum: 7,
        },
        default_settings: {
            scale: 5,
            labels: ["Très insatisfait", "Insatisfait", "Neutre", "Satisfait", "Très satisfait"],
        },
    },
    {
        identifier: "star_rating",
        name: "Notation étoiles",
        category: "rating",
        validation_schema: {
            type: "integer",
            minimum: 1,
            maximum: 5,
        },
    },
    {
        identifier: "nps_score",
        name: "Score NPS",
        category: "rating",
        validation_schema: {
            type: "integer",
            minimum: 0,
            maximum: 10,
        },
    },
    {
        identifier: "slider",
        name: "Curseur",
        category: "rating",
        validation_schema: {
            type: "number",
            minimum: 0,
            maximum: 100,
        },
    },

    // === CLASSEMENT ===
    {
        identifier: "ranking",
        name: "Classement",
        category: "ranking",
        validation_schema: {
            type: "array",
            items: { type: "string" },
            uniqueItems: true,
        },
    },
    {
        identifier: "matrix",
        name: "Matrice",
        category: "ranking",
        validation_schema: {
            type: "object",
            properties: {},
            additionalProperties: { type: "string" },
        },
    },

    // === MEDIA ===
    {
        identifier: "image_upload",
        name: "Upload image",
        category: "media",
        validation_schema: {
            type: "string",
            format: "uri",
        },
        requires_plugin: "media-upload",
    },
    {
        identifier: "file_upload",
        name: "Upload fichier",
        category: "media",
        validation_schema: {
            type: "string",
            format: "uri",
        },
        requires_plugin: "media-upload",
    },
    {
        identifier: "video_response",
        name: "Réponse vidéo",
        category: "media",
        validation_schema: {
            type: "string",
            format: "uri",
        },
        requires_plugin: "media-upload",
    },

    // === AVANCÉ ===
    {
        identifier: "signature",
        name: "Signature",
        category: "advanced",
        validation_schema: {
            type: "string",
            format: "data-url",
        },
    },
    {
        identifier: "drawing",
        name: "Dessin",
        category: "advanced",
        validation_schema: {
            type: "string",
            format: "data-url",
        },
    },
    {
        identifier: "geolocation",
        name: "Géolocalisation",
        category: "advanced",
        validation_schema: {
            type: "object",
            properties: {
                lat: { type: "number" },
                lng: { type: "number" },
                address: { type: "string" },
            },
            required: ["lat", "lng"],
        },
    },
];
```

### 2.2 Composants Frontend pour Chaque Type

```jsx
// frontend/src/components/questions/QuestionRenderer.jsx
import React from "react";
import { SingleChoice } from "./types/SingleChoice";
import { MultipleChoice } from "./types/MultipleChoice";
import { LikertScale } from "./types/LikertScale";
import { StarRating } from "./types/StarRating";
import { Slider } from "./types/Slider";
import { Ranking } from "./types/Ranking";
import { Matrix } from "./types/Matrix";
import { ImageUpload } from "./types/ImageUpload";
import { Signature } from "./types/Signature";
import { Geolocation } from "./types/Geolocation";
// ... autres imports

const questionComponents = {
    single_choice: SingleChoice,
    multiple_choice: MultipleChoice,
    dropdown: Dropdown,
    short_text: ShortText,
    long_text: LongText,
    email: EmailInput,
    phone: PhoneInput,
    number: NumberInput,
    likert_scale: LikertScale,
    star_rating: StarRating,
    nps_score: NPSScore,
    slider: Slider,
    ranking: Ranking,
    matrix: Matrix,
    image_upload: ImageUpload,
    file_upload: FileUpload,
    video_response: VideoResponse,
    signature: Signature,
    drawing: Drawing,
    geolocation: Geolocation,
};

export function QuestionRenderer({ question, value, onChange, error }) {
    const Component = questionComponents[question.type];

    if (!Component) {
        console.error(`Unknown question type: ${question.type}`);
        return null;
    }

    return (
        <div className="question-wrapper">
            <Component question={question} value={value} onChange={onChange} error={error} />
        </div>
    );
}
```

## 3. API Publique REST v1

### 3.1 Configuration API dans Strapi

```javascript
// src/api/v1/routes/polls.js
module.exports = {
    routes: [
        // Polls
        {
            method: "GET",
            path: "/v1/polls",
            handler: "api::poll.poll.find",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "List all polls",
            },
        },
        {
            method: "GET",
            path: "/v1/polls/:id",
            handler: "api::poll.poll.findOne",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Get poll details",
            },
        },
        {
            method: "POST",
            path: "/v1/polls",
            handler: "api::poll.poll.create",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Create a new poll",
            },
        },
        {
            method: "PUT",
            path: "/v1/polls/:id",
            handler: "api::poll.poll.update",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Update poll",
            },
        },
        {
            method: "DELETE",
            path: "/v1/polls/:id",
            handler: "api::poll.poll.delete",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Delete poll",
            },
        },

        // Responses
        {
            method: "POST",
            path: "/v1/polls/:id/responses",
            handler: "api::response.response.create",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Submit response",
            },
        },

        // Analytics
        {
            method: "GET",
            path: "/v1/polls/:id/analytics",
            handler: "api::poll.poll.analytics",
            config: {
                middlewares: ["api::v1.api-auth", "api::v1.rate-limit"],
                description: "Get poll analytics",
            },
        },
    ],
};
```

### 3.2 Middleware d'Authentification API

```javascript
// src/api/v1/middlewares/api-auth.js
module.exports = (config, { strapi }) => {
    return async (ctx, next) => {
        const apiKey = ctx.request.header["x-api-key"];

        if (!apiKey) {
            return ctx.unauthorized("API key required");
        }

        const key = await strapi.entityService.findOne("api::api-key.api-key", {
            filters: { key: apiKey, active: true },
        });

        if (!key) {
            return ctx.unauthorized("Invalid API key");
        }

        // Vérifier expiration
        if (key.expires_at && new Date(key.expires_at) < new Date()) {
            return ctx.unauthorized("API key expired");
        }

        // Attacher les permissions au contexte
        ctx.state.apiKey = key;
        ctx.state.permissions = key.permissions;

        // Tracking usage
        await strapi.service("api::api-key.api-key").trackUsage(key.id);

        await next();
    };
};
```

### 3.3 Rate Limiting

```javascript
// src/api/v1/middlewares/rate-limit.js
const rateLimit = require("koa-ratelimit");
const Redis = require("ioredis");

module.exports = (config, { strapi }) => {
    const db = new Redis(strapi.config.get("server.redis"));

    return rateLimit({
        driver: "redis",
        db: db,
        duration: 60 * 60 * 1000, // 1 heure
        errorMessage: "Rate limit exceeded",
        id: (ctx) => ctx.state.apiKey?.id || ctx.ip,
        headers: {
            remaining: "X-RateLimit-Remaining",
            reset: "X-RateLimit-Reset",
            total: "X-RateLimit-Limit",
        },
        max: (ctx) => ctx.state.apiKey?.rate_limit || 100,
        disableHeader: false,
        whitelist: (ctx) => {
            // Exemptions éventuelles
            return false;
        },
    });
};
```

### 3.4 Documentation OpenAPI

```javascript
// src/extensions/documentation/config/settings.js
module.exports = {
    openapi: "3.0.0",
    info: {
        title: "CivicPoll API",
        version: "1.0.0",
        description: "API publique pour intégrer CivicPoll dans vos applications",
        contact: {
            email: "<EMAIL>",
        },
    },
    servers: [
        {
            url: "https://civicpoll.fr.smatflow.xyz/api",
            description: "Production",
        },
    ],
    "x-strapi-config": {
        path: "/documentation",
        showGeneratedFiles: true,
        generateDefaultResponse: true,
    },
    security: [
        {
            ApiKeyAuth: [],
        },
    ],
    components: {
        securitySchemes: {
            ApiKeyAuth: {
                type: "apiKey",
                in: "header",
                name: "X-API-Key",
            },
        },
    },
};
```

## 4. Système de Webhooks

### 4.1 Service Webhook

```javascript
// src/api/webhook/services/webhook.js
const crypto = require("crypto");
const axios = require("axios");

module.exports = {
    async trigger(event, data) {
        const webhooks = await strapi.entityService.findMany("api::webhook.webhook", {
            filters: {
                events: { $contains: event },
                active: true,
            },
        });

        for (const webhook of webhooks) {
            await this.send(webhook, event, data);
        }
    },

    async send(webhook, event, data) {
        const payload = {
            event,
            data,
            timestamp: new Date().toISOString(),
            webhook_id: webhook.id,
        };

        // Signature HMAC
        const signature = this.generateSignature(payload, webhook.secret);

        try {
            const response = await axios.post(webhook.url, payload, {
                headers: {
                    "Content-Type": "application/json",
                    "X-CivicPoll-Signature": signature,
                    "X-CivicPoll-Event": event,
                },
                timeout: 10000,
            });

            await this.logSuccess(webhook.id, response.status);
        } catch (error) {
            await this.handleFailure(webhook, error, payload);
        }
    },

    generateSignature(payload, secret) {
        return crypto.createHmac("sha256", secret).update(JSON.stringify(payload)).digest("hex");
    },

    async handleFailure(webhook, error, payload) {
        const retryCount = webhook.failure_count || 0;

        await strapi.entityService.update("api::webhook.webhook", webhook.id, {
            data: {
                failure_count: retryCount + 1,
                last_error: error.message,
            },
        });

        // Retry logic
        if (retryCount < webhook.retry_policy.max_retries) {
            const delay = webhook.retry_policy.backoff_seconds[retryCount] * 1000;

            setTimeout(() => {
                this.send(webhook, payload.event, payload.data);
            }, delay);
        }
    },
};
```

### 4.2 Événements Webhook

```javascript
// src/api/poll/lifecycles.js
module.exports = {
    async afterCreate(event) {
        const { result } = event;

        await strapi.service("api::webhook.webhook").trigger("poll.created", {
            poll: result,
        });
    },

    async afterUpdate(event) {
        const { result } = event;

        await strapi.service("api::webhook.webhook").trigger("poll.updated", {
            poll: result,
        });
    },

    async afterDelete(event) {
        const { result } = event;

        await strapi.service("api::webhook.webhook").trigger("poll.deleted", {
            poll_id: result.id,
        });
    },
};

// src/api/response/lifecycles.js
module.exports = {
    async afterCreate(event) {
        const { result } = event;

        await strapi.service("api::webhook.webhook").trigger("response.received", {
            response: result,
            poll_id: result.poll.id,
        });

        // Vérifier si sondage terminé
        const poll = await strapi.entityService.findOne("api::poll.poll", result.poll.id, {
            populate: ["responses"],
        });

        if (this.isPollComplete(poll)) {
            await strapi.service("api::webhook.webhook").trigger("poll.completed", {
                poll: poll,
                total_responses: poll.responses.length,
            });
        }
    },
};
```

## 5. Notifications Multi-Canal

### 5.1 Service de Notification

```javascript
// src/api/notification/services/notification.js
module.exports = {
    providers: {
        email: require("./providers/email"),
        sms: require("./providers/sms"),
        push: require("./providers/push"),
        in_app: require("./providers/in-app"),
    },

    async send(type, channel, recipient, data) {
        const template = await this.getTemplate(type, channel);
        const content = await this.renderTemplate(template, data);

        const notification = await strapi.entityService.create("api::notification.notification", {
            data: {
                type,
                channel,
                recipient: recipient.id,
                subject: content.subject,
                content: content.body,
                template_id: template.id,
                variables: data,
                poll: data.poll?.id,
            },
        });

        try {
            const provider = this.providers[channel];
            const result = await provider.send(recipient, content, data);

            await this.updateStatus(notification.id, "sent", result);
        } catch (error) {
            await this.updateStatus(notification.id, "failed", { error: error.message });
            throw error;
        }
    },

    async sendBatch(notifications) {
        const queue = strapi.service("queue");

        for (const notif of notifications) {
            await queue.add("notification", notif, {
                attempts: 3,
                backoff: {
                    type: "exponential",
                    delay: 2000,
                },
            });
        }
    },

    async getTemplate(type, channel) {
        // Récupérer le template approprié
        return strapi.db.query("api::notification-template.notification-template").findOne({
            where: { type, channel },
        });
    },

    async renderTemplate(template, data) {
        const Handlebars = require("handlebars");

        const subjectTemplate = Handlebars.compile(template.subject);
        const bodyTemplate = Handlebars.compile(template.body);

        return {
            subject: subjectTemplate(data),
            body: bodyTemplate(data),
        };
    },
};
```

### 5.2 Providers de Notification

```javascript
// src/api/notification/services/providers/email.js
const sgMail = require("@sendgrid/mail");

module.exports = {
    async send(recipient, content, data) {
        sgMail.setApiKey(process.env.SENDGRID_API_KEY);

        const msg = {
            to: recipient.email,
            from: {
                email: "<EMAIL>",
                name: "CivicPoll",
            },
            subject: content.subject,
            html: content.body,
            templateId: data.templateId,
            dynamicTemplateData: data,
        };

        const [response] = await sgMail.send(msg);

        return {
            provider: "sendgrid",
            message_id: response.headers["x-message-id"],
            status: response.statusCode,
        };
    },
};

// src/api/notification/services/providers/sms.js
const twilio = require("twilio");

module.exports = {
    async send(recipient, content, data) {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

        const message = await client.messages.create({
            body: content.body,
            from: process.env.TWILIO_PHONE_NUMBER,
            to: recipient.phone,
        });

        return {
            provider: "twilio",
            message_id: message.sid,
            status: message.status,
        };
    },
};
```

## 6. Bibliothèque de Templates

### 6.1 Import des Templates

```javascript
// scripts/import-templates.js
const templates = [
    // === RH ===
    {
        name: "Satisfaction Employés",
        category: "rh",
        methodology: "enps",
        description: "Mesurez la satisfaction et l'engagement de vos équipes",
        questions_structure: [
            {
                text: "Recommanderiez-vous votre entreprise comme lieu de travail ?",
                type: "nps_score",
                required: true,
            },
            {
                text: "Qu'est-ce que vous appréciez le plus dans votre travail ?",
                type: "multiple_choice",
                options: ["L'équipe", "Les missions", "L'ambiance", "L'évolution", "Le salaire"],
                required: true,
            },
            {
                text: "Comment évaluez-vous ces aspects ?",
                type: "matrix",
                rows: ["Management", "Communication", "Formation", "Équilibre vie pro/perso"],
                columns: [
                    "Très insatisfait",
                    "Insatisfait",
                    "Neutre",
                    "Satisfait",
                    "Très satisfait",
                ],
                required: true,
            },
        ],
    },

    // === MARKETING ===
    {
        name: "Étude de Marché",
        category: "marketing",
        description: "Validez votre concept auprès de votre marché cible",
        questions_structure: [
            {
                text: "Quelle est votre tranche d'âge ?",
                type: "single_choice",
                options: ["18-24", "25-34", "35-44", "45-54", "55+"],
                required: true,
            },
            {
                text: "À quelle fréquence utilisez-vous ce type de produit ?",
                type: "single_choice",
                options: ["Quotidiennement", "Hebdomadaire", "Mensuel", "Rarement", "Jamais"],
                required: true,
            },
            {
                text: "Quel prix seriez-vous prêt à payer ?",
                type: "slider",
                min: 0,
                max: 100,
                unit: "€",
                required: true,
            },
        ],
    },

    // === ÉVÉNEMENTIEL ===
    {
        name: "Feedback Événement",
        category: "evenementiel",
        description: "Recueillez les retours après votre événement",
        questions_structure: [
            {
                text: "Comment évaluez-vous l'événement dans son ensemble ?",
                type: "star_rating",
                required: true,
            },
            {
                text: "Qu'avez-vous préféré ?",
                type: "multiple_choice",
                options: ["Conférences", "Networking", "Ateliers", "Exposants", "Animations"],
                required: true,
            },
            {
                text: "Recommanderiez-vous cet événement ?",
                type: "nps_score",
                required: true,
            },
            {
                text: "Suggestions d'amélioration",
                type: "long_text",
                required: false,
            },
        ],
    },

    // === SATISFACTION CLIENT ===
    {
        name: "CSAT - Satisfaction Client",
        category: "satisfaction",
        methodology: "csat",
        description: "Mesurez la satisfaction après un achat ou un service",
        questions_structure: [
            {
                text: "Êtes-vous satisfait de votre expérience ?",
                type: "likert_scale",
                scale: 5,
                required: true,
            },
            {
                text: "Qu'est-ce qui a influencé votre évaluation ?",
                type: "multiple_choice",
                options: ["Qualité", "Prix", "Service", "Délai", "Communication"],
                required: true,
            },
            {
                text: "Commentaires additionnels",
                type: "long_text",
                required: false,
            },
        ],
    },

    // === POLITIQUE ===
    {
        name: "Consultation Citoyenne",
        category: "politique",
        description: "Consultez les citoyens sur un projet public",
        questions_structure: [
            {
                text: "Êtes-vous favorable à ce projet ?",
                type: "single_choice",
                options: [
                    "Très favorable",
                    "Plutôt favorable",
                    "Neutre",
                    "Plutôt défavorable",
                    "Très défavorable",
                ],
                required: true,
            },
            {
                text: "Quels aspects vous préoccupent ?",
                type: "multiple_choice",
                options: ["Impact environnemental", "Coût", "Nuisances", "Utilité", "Esthétique"],
                required: true,
            },
            {
                text: "Proposez des améliorations",
                type: "long_text",
                required: false,
            },
            {
                text: "Localisez votre suggestion sur la carte",
                type: "geolocation",
                required: false,
            },
        ],
    },
];

// Plus 15 autres templates...
```

## 7. Frontend Enrichi

### 7.1 Éditeur de Questions Avancé

```jsx
// frontend/src/components/poll-builder/QuestionEditor.jsx
import React, { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { QuestionTypeSelector } from "./QuestionTypeSelector";
import { QuestionSettings } from "./QuestionSettings";
import { QuestionPreview } from "./QuestionPreview";

export function QuestionEditor({ questions, onChange }) {
    const [selectedQuestion, setSelectedQuestion] = useState(null);

    const handleDragEnd = (result) => {
        if (!result.destination) return;

        const items = Array.from(questions);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        onChange(items);
    };

    const addQuestion = (type) => {
        const newQuestion = {
            id: `q_${Date.now()}`,
            type,
            text: "",
            required: true,
            settings: getDefaultSettings(type),
        };

        onChange([...questions, newQuestion]);
        setSelectedQuestion(newQuestion.id);
    };

    return (
        <div className="question-editor">
            <div className="editor-sidebar">
                <QuestionTypeSelector onSelect={addQuestion} />

                {selectedQuestion && (
                    <QuestionSettings
                        question={questions.find((q) => q.id === selectedQuestion)}
                        onChange={(updated) => {
                            const index = questions.findIndex((q) => q.id === selectedQuestion);
                            const newQuestions = [...questions];
                            newQuestions[index] = updated;
                            onChange(newQuestions);
                        }}
                    />
                )}
            </div>

            <div className="editor-main">
                <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="questions">
                        {(provided) => (
                            <div {...provided.droppableProps} ref={provided.innerRef}>
                                {questions.map((question, index) => (
                                    <Draggable
                                        key={question.id}
                                        draggableId={question.id}
                                        index={index}
                                    >
                                        {(provided, snapshot) => (
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                className={`question-item ${snapshot.isDragging ? "dragging" : ""}`}
                                            >
                                                <div
                                                    className="drag-handle"
                                                    {...provided.dragHandleProps}
                                                >
                                                    ⋮⋮
                                                </div>

                                                <QuestionPreview
                                                    question={question}
                                                    number={index + 1}
                                                    isSelected={selectedQuestion === question.id}
                                                    onClick={() => setSelectedQuestion(question.id)}
                                                />

                                                <button
                                                    className="delete-question"
                                                    onClick={() => {
                                                        onChange(
                                                            questions.filter(
                                                                (q) => q.id !== question.id,
                                                            ),
                                                        );
                                                    }}
                                                >
                                                    ×
                                                </button>
                                            </div>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
            </div>
        </div>
    );
}
```

### 7.2 Templates Gallery

```jsx
// frontend/src/components/templates/TemplateGallery.jsx
import React, { useState, useEffect } from "react";
import { TemplateCard } from "./TemplateCard";
import { TemplateFilters } from "./TemplateFilters";
import { TemplatePreview } from "./TemplatePreview";

export function TemplateGallery({ onSelect }) {
    const [templates, setTemplates] = useState([]);
    const [filters, setFilters] = useState({
        category: "all",
        methodology: "all",
        search: "",
    });
    const [preview, setPreview] = useState(null);

    useEffect(() => {
        loadTemplates();
    }, [filters]);

    const loadTemplates = async () => {
        const params = new URLSearchParams();
        if (filters.category !== "all") params.append("category", filters.category);
        if (filters.methodology !== "all") params.append("methodology", filters.methodology);
        if (filters.search) params.append("search", filters.search);

        const response = await fetch(`${API_BASE_URL}/templates?${params}`);
        const data = await response.json();
        setTemplates(data.data);
    };

    const filteredTemplates = templates.filter((template) => {
        if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            return (
                template.name.toLowerCase().includes(searchLower) ||
                template.description.toLowerCase().includes(searchLower)
            );
        }
        return true;
    });

    return (
        <div className="template-gallery">
            <TemplateFilters filters={filters} onChange={setFilters} />

            <div className="templates-grid">
                {filteredTemplates.map((template) => (
                    <TemplateCard
                        key={template.id}
                        template={template}
                        onPreview={() => setPreview(template)}
                        onSelect={() => onSelect(template)}
                    />
                ))}
            </div>

            {preview && (
                <TemplatePreview
                    template={preview}
                    onClose={() => setPreview(null)}
                    onUse={() => {
                        onSelect(preview);
                        setPreview(null);
                    }}
                />
            )}
        </div>
    );
}
```

## 8. API Playground

### 8.1 Interface de Test API

```jsx
// frontend/src/components/api/APIPlayground.jsx
import React, { useState } from "react";
import { CodeEditor } from "./CodeEditor";
import { ResponseViewer } from "./ResponseViewer";
import { APIDocumentation } from "./APIDocumentation";

export function APIPlayground() {
    const [request, setRequest] = useState({
        method: "GET",
        endpoint: "/v1/polls",
        headers: {
            "X-API-Key": "",
            "Content-Type": "application/json",
        },
        body: "",
    });
    const [response, setResponse] = useState(null);
    const [loading, setLoading] = useState(false);

    const executeRequest = async () => {
        setLoading(true);

        try {
            const options = {
                method: request.method,
                headers: request.headers,
            };

            if (["POST", "PUT", "PATCH"].includes(request.method)) {
                options.body = request.body;
            }

            const res = await fetch(`${API_BASE_URL}${request.endpoint}`, options);
            const data = await res.json();

            setResponse({
                status: res.status,
                statusText: res.statusText,
                headers: Object.fromEntries(res.headers.entries()),
                body: data,
            });
        } catch (error) {
            setResponse({
                error: error.message,
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="api-playground">
            <div className="playground-header">
                <h2>API Playground</h2>
                <button className="docs-link" onClick={() => window.open("/documentation")}>
                    📚 Documentation complète
                </button>
            </div>

            <div className="playground-content">
                <div className="request-section">
                    <div className="request-line">
                        <select
                            value={request.method}
                            onChange={(e) => setRequest({ ...request, method: e.target.value })}
                        >
                            <option>GET</option>
                            <option>POST</option>
                            <option>PUT</option>
                            <option>DELETE</option>
                        </select>

                        <input
                            type="text"
                            value={request.endpoint}
                            onChange={(e) => setRequest({ ...request, endpoint: e.target.value })}
                            placeholder="/v1/polls"
                        />

                        <button onClick={executeRequest} disabled={loading} className="execute-btn">
                            {loading ? "Exécution..." : "Exécuter"}
                        </button>
                    </div>

                    <div className="headers-section">
                        <h3>Headers</h3>
                        <CodeEditor
                            value={JSON.stringify(request.headers, null, 2)}
                            onChange={(value) => {
                                try {
                                    setRequest({ ...request, headers: JSON.parse(value) });
                                } catch (e) {}
                            }}
                            language="json"
                        />
                    </div>

                    {["POST", "PUT", "PATCH"].includes(request.method) && (
                        <div className="body-section">
                            <h3>Body</h3>
                            <CodeEditor
                                value={request.body}
                                onChange={(value) => setRequest({ ...request, body: value })}
                                language="json"
                            />
                        </div>
                    )}
                </div>

                <div className="response-section">
                    <h3>Response</h3>
                    {response && <ResponseViewer response={response} />}
                </div>
            </div>

            <APIDocumentation endpoint={request.endpoint} />
        </div>
    );
}
```

## 9. Tests et Qualité

### 9.1 Tests Unitaires Types de Questions

```javascript
// tests/unit/question-types.test.js
describe("Question Types Validation", () => {
    const types = require("../../src/api/question-types/data/types");

    types.forEach((type) => {
        describe(`${type.name} (${type.identifier})`, () => {
            test("should have valid schema", () => {
                expect(type.validation_schema).toBeDefined();
                expect(type.validation_schema.type).toBeDefined();
            });

            test("should validate correct input", () => {
                const validator = ajv.compile(type.validation_schema);
                const testData = generateValidData(type);
                expect(validator(testData)).toBe(true);
            });

            test("should reject invalid input", () => {
                const validator = ajv.compile(type.validation_schema);
                const testData = generateInvalidData(type);
                expect(validator(testData)).toBe(false);
            });
        });
    });
});
```

### 9.2 Tests API

```javascript
// tests/integration/api-v1.test.js
describe("API v1", () => {
    let apiKey;

    beforeAll(async () => {
        // Créer une clé API de test
        const key = await strapi.entityService.create("api::api-key.api-key", {
            data: {
                name: "Test Key",
                key: "test_" + crypto.randomBytes(16).toString("hex"),
                permissions: ["read:polls", "create:polls"],
            },
        });
        apiKey = key.key;
    });

    describe("GET /v1/polls", () => {
        test("should require API key", async () => {
            const response = await request(strapi.server).get("/api/v1/polls").expect(401);

            expect(response.body.error).toContain("API key required");
        });

        test("should return polls with valid API key", async () => {
            const response = await request(strapi.server)
                .get("/api/v1/polls")
                .set("X-API-Key", apiKey)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
        });

        test("should respect rate limits", async () => {
            // Faire 101 requêtes (limite = 100)
            for (let i = 0; i < 101; i++) {
                const response = await request(strapi.server)
                    .get("/api/v1/polls")
                    .set("X-API-Key", apiKey);

                if (i === 100) {
                    expect(response.status).toBe(429);
                    expect(response.body.error).toContain("Rate limit exceeded");
                }
            }
        });
    });

    describe("Webhooks", () => {
        test("should trigger webhook on poll creation", async (done) => {
            // Créer un serveur mock pour recevoir le webhook
            const webhookServer = createWebhookMockServer((req) => {
                expect(req.headers["x-civicpoll-event"]).toBe("poll.created");
                expect(req.body.event).toBe("poll.created");
                expect(req.body.data.poll).toBeDefined();
                done();
            });

            // Créer un webhook
            await strapi.entityService.create("api::webhook.webhook", {
                data: {
                    url: "http://localhost:3001/webhook",
                    events: ["poll.created"],
                    secret: "test_secret",
                },
            });

            // Créer un sondage
            await strapi.entityService.create("api::poll.poll", {
                data: {
                    title: "Test Poll",
                    description: "Test",
                },
            });
        });
    });
});
```

## 10. Déploiement Phase 2

### 10.1 Script de Migration

```bash
#!/bin/bash
# deploy-phase2.sh

echo "🚀 Déploiement Phase 2 - Enrichissement et API..."

# 1. Backup
./scripts/backup.sh

# 2. Update code
cd /opt/civicpoll/app
git pull origin main

# 3. Install new dependencies
npm ci --production

# 4. Run migrations
npm run strapi migrate

# 5. Import question types
node scripts/import-question-types.js

# 6. Import templates
node scripts/import-templates.js

# 7. Build frontend
cd frontend
npm ci
npm run build

# 8. Generate API documentation
cd ..
npm run docs:generate

# 9. Restart services
pm2 restart civicpoll

# 10. Run tests
npm run test:api

echo "✅ Phase 2 déployée avec succès"
```

### 10.2 Configuration Nginx pour API

```nginx
# API v1 routes
location /api/v1/ {
    proxy_pass http://localhost:1337/api/v1/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;

    # CORS pour API
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'X-API-Key, Content-Type';
}

# Documentation API
location /documentation {
    proxy_pass http://localhost:1337/documentation;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
}
```

---

## ✅ VALIDATION FINALE DE LA PHASE 2

### Tests Fonctionnels

- [ ] 20 types de questions opérationnels
- [ ] 20+ templates disponibles
- [ ] API REST v1 documentée
- [ ] Webhooks fonctionnels
- [ ] Notifications email/SMS actives
- [ ] Upload médias fonctionnel

### Tests API

- [ ] Authentication par API key
- [ ] Rate limiting effectif
- [ ] Documentation OpenAPI complète
- [ ] Webhooks avec retry
- [ ] Signatures HMAC validées

### Tests Performance

- [ ] API < 100ms latence moyenne
- [ ] Upload fichiers < 10MB OK
- [ ] Batch notifications scalable
- [ ] 1000 req/min supportées

---

_Document technique - Phase 2 - Version 1.0_
