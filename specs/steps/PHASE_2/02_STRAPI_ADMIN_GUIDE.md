# Phase 2 - Guide Administrateur Strapi : API et Enrichissement

## Introduction

La Phase 2 transforme CivicPoll en une plateforme riche et extensible. Vous allez découvrir comment gérer les 20 types de questions, la bibliothèque de templates, l'API publique, les webhooks et les notifications multi-canal.

## 1. Gestion des Types de Questions

### 1.1 Vue d'Ensemble des 20 Types

Dans **Content Manager → Question Types**, vous trouvez les 20 types organisés par catégorie :

**Questions de Choix** 📋

- Choix unique
- Choix multiple
- Liste déroulante

**Questions Textuelles** ✏️

- Texte court (max 255 car.)
- Texte long (max 5000 car.)
- Email (validation automatique)
- Téléphone (formats internationaux)
- Nombre (avec min/max)

**Questions de Notation** ⭐

- Échelle de <PERSON>rt (1-7)
- Notation étoiles (1-5)
- Score NPS (0-10)
- Curseur (0-100)

**Questions de Classement** 🏆

- Classement par ordre
- Matrice de choix

**Questions Médias** 📷

- Upload d'image
- Upload de fichier
- Réponse vidéo

**Questions Avancées** 🚀

- Signature électronique
- Dessin libre
- Géolocalisation

### 1.2 Configuration d'un Type de Question

Pour chaque type, vous pouvez configurer :

**Validation** :

```json
{
    "validation_schema": {
        "type": "string",
        "minLength": 10,
        "maxLength": 500,
        "pattern": "^[A-Za-z0-9 ]+$"
    }
}
```

**Paramètres par défaut** :

```json
{
    "default_settings": {
        "placeholder": "Votre réponse...",
        "required": true,
        "help_text": "Minimum 10 caractères"
    }
}
```

### 1.3 Personnaliser les Types

**Modifier un type existant** :

1. **Content Manager → Question Types**
2. Sélectionnez le type
3. Modifiez les paramètres
4. **⚠️ Attention** : Les modifications affectent tous les sondages futurs

**Ajouter des validations** :

- Regex pour formats spécifiques
- Limites min/max
- Listes de valeurs autorisées
- Dépendances entre questions

### 1.4 Types Médias - Configuration

**Upload d'images** :

- Formats : JPG, PNG, WebP, GIF
- Taille max : 5MB par défaut
- Dimensions : Redimensionnement auto
- Stockage : Local ou S3

**Upload de fichiers** :

- Formats : PDF, DOC, XLS, etc.
- Taille max : 10MB
- Antivirus : Scan automatique
- Métadonnées : Extraction auto

**Réponses vidéo** :

- Durée max : 2 minutes
- Formats : MP4, WebM
- Compression : Automatique
- Streaming : HLS si activé

## 2. Bibliothèque de Templates

### 2.1 Templates Prédéfinis

**Content Manager → Templates**

20+ templates organisés par secteur :

**RH / Ressources Humaines** 👥

- Satisfaction employés (eNPS)
- Évaluation 360°
- Feedback onboarding
- Climat social
- Exit interview

**Marketing** 📊

- Étude de marché
- Test de concept
- Satisfaction client (CSAT)
- Net Promoter Score (NPS)
- Feedback produit

**Politique / Consultation** 🏛️

- Consultation citoyenne
- Budget participatif
- Évaluation de projet
- Sondage d'opinion

**Événementiel** 🎉

- Feedback participant
- Inscription avec préférences
- Évaluation conférencier
- Satisfaction globale

### 2.2 Créer un Template

1. **"+ Add new entry"** dans Templates

2. **Informations de base** :

    - **Name** : Nom explicite
    - **Category** : Secteur d'application
    - **Description** : Usage et contexte
    - **Methodology** : NPS, CSAT, etc.

3. **Structure des questions** (JSON) :

```json
{
    "questions": [
        {
            "text": "Comment évaluez-vous notre service ?",
            "type": "likert_scale",
            "required": true,
            "settings": {
                "scale": 5,
                "labels": ["Très mauvais", "Mauvais", "Neutre", "Bon", "Excellent"]
            }
        },
        {
            "text": "Qu'est-ce qui pourrait être amélioré ?",
            "type": "multiple_choice",
            "required": false,
            "options": ["Rapidité", "Qualité", "Communication", "Prix"],
            "settings": {
                "allow_other": true,
                "max_selections": 3
            }
        }
    ]
}
```

4. **Bonnes pratiques** :
    - Ordre logique des questions
    - Mix de types pour engagement
    - Questions conditionnelles
    - Temps estimé réaliste

### 2.3 Gérer les Templates

**Statistiques d'utilisation** :

```
Template "Satisfaction Client CSAT"
├── Utilisé : 127 fois
├── Taux complétion moyen : 84%
├── Note moyenne : 4.2/5
└── Organisations : 23
```

**Dupliquer et personnaliser** :

1. Sélectionnez un template
2. **Actions → Duplicate**
3. Modifiez selon vos besoins
4. Sauvegardez comme nouveau

**Partage de templates** :

- Templates publics : Visibles par tous
- Templates privés : Organisation seule
- Templates premium : Marketplace (Phase 7)

### 2.4 Import/Export de Templates

**Exporter** :

1. Sélectionnez templates
2. **Actions → Export**
3. Format JSON téléchargé

**Importer** :

1. **"Import templates"**
2. Upload fichier JSON
3. Validation automatique
4. Fusion ou écrasement

## 3. API Publique v1

### 3.1 Configuration de l'API

**Settings → API Configuration**

**Activation** :

- [ ] API v1 activée
- [ ] Documentation publique
- [ ] Playground activé
- [ ] Webhooks actifs

**Rate Limiting** :

```
Limites par défaut :
├── Anonymous : 10 req/heure
├── Free tier : 100 req/heure
├── Pro tier : 1000 req/heure
└── Enterprise : Illimité
```

### 3.2 Gestion des Clés API

**Content Manager → API Keys**

**Créer une clé** :

1. **"+ Add new entry"**
2. **Name** : Application cliente
3. **Permissions** :
    - `read:polls` - Lecture sondages
    - `create:polls` - Création
    - `read:responses` - Voir réponses
    - `read:analytics` - Analytics
4. **Rate limit** : Requêtes/heure
5. **Expiration** : Date limite

**Clé générée** :

```
civicpoll_live_sk_a1b2c3d4e5f6...
```

**Sécurité** :

- Clés chiffrées en base
- Rotation recommandée
- Logs d'utilisation
- Blocage si compromis

### 3.3 Monitoring API

**Dashboard API** :

```
Utilisation API - Aujourd'hui
├── Requêtes totales : 12,456
├── Requêtes/minute : 8.7
├── Erreurs 4xx : 23 (0.18%)
├── Erreurs 5xx : 2 (0.01%)
├── Latence moyenne : 87ms
└── Top endpoints :
    ├── GET /polls : 45%
    ├── POST /responses : 32%
    └── GET /analytics : 23%
```

**Par clé API** :

```
Clé : "App Mobile ACME"
├── Requêtes : 3,234/10,000
├── Reset dans : 42 min
├── Dernière utilisation : il y a 2 min
└── Top endpoints utilisés
```

### 3.4 Documentation Interactive

**Accès** : `/documentation`

**Swagger UI intégré** :

- Tous les endpoints documentés
- Schémas de données
- Exemples de requêtes
- Try it out en direct

**Maintenir à jour** :

- Auto-généré depuis code
- Commentaires JSDoc
- Exemples réalistes
- Codes d'erreur détaillés

## 4. Système de Webhooks

### 4.1 Configuration des Webhooks

**Content Manager → Webhooks**

**Créer un webhook** :

1. **URL** : https://votre-app.com/webhook
2. **Events** : Cochez les événements
    - `poll.created`
    - `poll.updated`
    - `poll.completed`
    - `response.received`
    - `response.updated`
3. **Secret** : Clé pour signature HMAC
4. **Retry policy** : 3 tentatives

### 4.2 Format des Webhooks

**Structure d'un webhook** :

```json
{
    "event": "poll.completed",
    "data": {
        "poll": {
            "id": "poll_123",
            "title": "Satisfaction Client",
            "total_responses": 150,
            "completed_at": "2024-02-14T15:30:00Z"
        }
    },
    "timestamp": "2024-02-14T15:30:00Z",
    "webhook_id": "wh_456"
}
```

**Headers envoyés** :

```
X-CivicPoll-Signature: sha256=abc123...
X-CivicPoll-Event: poll.completed
X-CivicPoll-Webhook-ID: wh_456
Content-Type: application/json
```

### 4.3 Debugging Webhooks

**Logs des webhooks** :
**Monitoring → Webhook Logs**

Pour chaque envoi :

- Status HTTP reçu
- Temps de réponse
- Tentatives effectuées
- Payload envoyé
- Erreurs éventuelles

**Test manuel** :

1. Sélectionnez webhook
2. **Actions → Test**
3. Choisissez événement
4. Payload de test envoyé

### 4.4 Sécurité Webhooks

**Validation côté client** :

```javascript
// Exemple validation signature
const crypto = require("crypto");

function validateWebhook(payload, signature, secret) {
    const hash = crypto.createHmac("sha256", secret).update(JSON.stringify(payload)).digest("hex");

    return `sha256=${hash}` === signature;
}
```

**Bonnes pratiques** :

- HTTPS obligatoire
- Validation signature
- IP whitelist si possible
- Timeout court (10s)

## 5. Notifications Multi-Canal

### 5.1 Configuration des Canaux

**Settings → Notifications**

**Email (SendGrid)** 📧

```
Provider : SendGrid
API Key : ••••••••
From : <EMAIL>
From Name : CivicPoll
Templates : ✓ Activés
```

**SMS (Twilio)** 📱

```
Provider : Twilio
Account SID : ••••••••
Auth Token : ••••••••
From Number : +33 7 12 34 56 78
Pays autorisés : FR, BE, CH
```

**Push (à venir)** 🔔

```
Provider : OneSignal
App ID : ••••••••
API Key : ••••••••
```

### 5.2 Templates de Notification

**Content Manager → Notification Templates**

Templates par défaut :

**Invitation sondage** :

- Email : HTML riche avec bouton
- SMS : Texte court + lien

**Rappel participation** :

- Email : Personnalisé avec deadline
- SMS : "Plus que 24h pour participer !"

**Résultats disponibles** :

- Email : Graphiques + lien détails
- In-app : Notification badge

### 5.3 Créer un Template

**Variables disponibles** :

```handlebars
{{user.name}}
- Nom utilisateur
{{poll.title}}
- Titre sondage
{{poll.end_date}}
- Date fin
{{poll.url}}
- Lien direct
{{organization.name}}
- Nom orga
{{custom.variable}}
- Variables custom
```

**Template Email exemple** :

```html
<!DOCTYPE html>
<html>
    <head>
        <style>
            .button {
                background: #3b82f6;
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 6px;
            }
        </style>
    </head>
    <body>
        <h2>Bonjour {{user.name}} 👋</h2>

        <p>Un nouveau sondage vous attend :</p>
        <h3>{{poll.title}}</h3>

        <p>{{poll.description}}</p>

        <p>⏰ Disponible jusqu'au {{poll.end_date}}</p>

        <a href="{{poll.url}}" class="button"> Participer maintenant </a>

        <p>Merci pour votre participation !</p>
        <p>L'équipe {{organization.name}}</p>
    </body>
</html>
```

### 5.4 Règles d'Envoi

**Automatismes** :

1. **À la création** : Notification zone ciblée
2. **J-1** : Rappel non-participants
3. **Clôture** : Remerciements + résultats
4. **Milestones** : 50%, 100 participants

**Préférences utilisateur** :

- Canal préféré (email/SMS)
- Fréquence (immédiat/digest)
- Opt-out par type

**Anti-spam** :

- Max 3 emails/jour/user
- Cooldown 1h entre SMS
- Respect horaires (8h-20h)

## 6. Analytics Enrichis

### 6.1 Nouvelles Métriques

**Par type de question** :

```
Question "Satisfaction" (Likert)
├── Réponses : 234
├── Moyenne : 4.2/5
├── Distribution :
│   ├── 1 : 2%
│   ├── 2 : 5%
│   ├── 3 : 18%
│   ├── 4 : 45%
│   └── 5 : 30%
└── Taux skip : 3%
```

**Questions médias** :

```
Upload images :
├── Total : 67 images
├── Taille moyenne : 1.2 MB
├── Formats : JPG 78%, PNG 22%
└── Contenu : Photos produits 90%
```

### 6.2 Export Avancé

**Nouveaux formats** :

- Excel avec graphiques
- PowerBI dataset
- Tableau extract
- JSON structuré
- CSV avec métadonnées

**Options d'export** :

- [ ] Données brutes
- [ ] Données agrégées
- [ ] Graphiques inclus
- [ ] Métadonnées questions
- [ ] Timestamps détaillés

### 6.3 Rapports Automatiques

**Configuration** :

1. **Reports → Scheduled Reports**
2. Fréquence : Quotidien/Hebdo/Mensuel
3. Destinataires : Emails
4. Contenu : Métriques choisies
5. Format : PDF ou Excel

**Exemple rapport hebdo** :

```
Rapport CivicPoll - Semaine 7

Activité :
✓ 12 sondages actifs
✓ 3,456 participations
✓ 287 nouveaux inscrits

Top sondages :
1. "Budget Participatif" - 890 votes
2. "Satisfaction Transport" - 567 votes
3. "Aménagement Parc" - 445 votes

Insights :
- Pic participation mardi 14h
- Questions vidéo +45% engagement
- NPS global : 72 (Excellent)
```

## 7. Gestion des Médias

### 7.1 Configuration Stockage

**Settings → Media Library**

**Stockage Local** (défaut) :

```
Path : /opt/civicpoll/uploads
Max size : 10 MB/fichier
Total : 50 GB alloués
Utilisé : 12.3 GB (24%)
```

**Stockage S3** (recommandé) :

```
Provider : AWS S3
Bucket : civicpoll-media
Region : eu-west-3
CDN : CloudFront activé
```

### 7.2 Traitement des Médias

**Images** :

- Redimensionnement auto
- Formats : Original + 3 tailles
- Compression : 85% qualité
- EXIF : Données supprimées

**Vidéos** :

- Transcoding : H.264/H.265
- Résolutions : 720p, 480p
- Durée max : 2 minutes
- Thumbnails : Auto-générés

### 7.3 Modération

**Détection automatique** :

- Contenu inapproprié (AI)
- Texte dans images (OCR)
- Visages (floutage RGPD)
- Métadonnées sensibles

**File d'attente modération** :

```
En attente : 12 médias
├── Images : 8
├── Vidéos : 3
└── Documents : 1

Actions : Approuver / Rejeter / Flouter
```

## 8. Performance et Optimisation

### 8.1 Cache Strategy

**Redis Cache** :

```
Templates : TTL 1 heure
API responses : TTL 5 minutes
User sessions : TTL 24 heures
Analytics : TTL 15 minutes
```

**Invalidation** :

- Auto sur modification
- Manuel si nécessaire
- Warmup quotidien

### 8.2 Queue Management

**Bull Dashboard** : `/admin/queues`

**Files d'attente** :

```
notifications :
├── Active : 23
├── Waiting : 156
├── Completed : 12,345
└── Failed : 2

webhooks :
├── Active : 5
├── Waiting : 34
├── Completed : 8,901
└── Failed : 12
```

**Priorités** :

1. Notifications urgentes
2. Webhooks critiques
3. Emails standard
4. Analytics batch

### 8.3 Monitoring Avancé

**Métriques clés** :

```
API Performance :
├── p50 : 45ms
├── p90 : 123ms
├── p99 : 234ms
└── p99.9 : 567ms

Errors (24h) :
├── 4xx : 0.1%
├── 5xx : 0.01%
└── Timeouts : 0.001%
```

## 9. Sécurité Phase 2

### 9.1 API Security

**Mesures actives** :

- Rate limiting par IP/clé
- CORS configuré
- Headers sécurité
- Validation entrées

**Monitoring** :

```
Tentatives suspectes (24h) :
├── Brute force API : 3
├── Injection SQL : 0
├── XSS attempts : 1
└── Actions : IP banned
```

### 9.2 Upload Security

**Validations** :

- Type MIME vérifié
- Extension whitelist
- Taille limitée
- Scan antivirus
- Sandbox execution

**Quarantaine** :

```
Fichiers en quarantaine : 2
├── malware.exe (virus détecté)
└── script.php (type interdit)
```

## 10. Troubleshooting Phase 2

### 10.1 API ne répond pas

**Vérifier** :

1. Status service : `pm2 status`
2. Logs API : `pm2 logs civicpoll`
3. Rate limits atteints ?
4. Clé API valide ?

### 10.2 Webhooks non reçus

**Debug** :

1. Logs webhooks → filtrer par URL
2. Vérifier HTTPS valide
3. Tester manuellement
4. Augmenter timeout si lent

### 10.3 Templates corrompus

**Réparer** :

1. Export backup templates
2. Valider JSON structure
3. Réimporter corrigés
4. Tester sur sondage draft

### 10.4 Notifications non envoyées

**Checklist** :

- [ ] Provider configuré ?
- [ ] Credits suffisants ?
- [ ] Templates valides ?
- [ ] Queue bloquée ?
- [ ] User preferences ?

## 11. Migration Phase 1 → Phase 2

### 11.1 Checklist Pré-Migration

- [ ] Backup complet effectué
- [ ] Maintenance planifiée annoncée
- [ ] Tests en staging validés
- [ ] Documentation équipe prête

### 11.2 Étapes Migration

1. **Import types questions** (30 min)
2. **Import templates** (15 min)
3. **Configuration API** (20 min)
4. **Setup notifications** (45 min)
5. **Tests validation** (1h)

### 11.3 Rollback Plan

Si problème :

1. `./scripts/rollback-phase2.sh`
2. Restaurer backup
3. Analyser logs
4. Corriger et réessayer

## 12. Checklist Admin Phase 2

### Configuration ✓

- [ ] 20 types questions actifs
- [ ] 20+ templates importés
- [ ] API v1 configurée
- [ ] Clés API créées pour tests
- [ ] Webhooks exemple configurés
- [ ] Notifications email testées
- [ ] SMS configuré (si applicable)

### Tests ✓

- [ ] Chaque type question testé
- [ ] Templates utilisables
- [ ] API répond correctement
- [ ] Webhooks reçus
- [ ] Emails envoyés
- [ ] Uploads fonctionnels

### Monitoring ✓

- [ ] Dashboard API visible
- [ ] Logs accessibles
- [ ] Alertes configurées
- [ ] Backups vérifiés

### Documentation ✓

- [ ] Guide API publié
- [ ] Exemples webhooks
- [ ] Templates documentés
- [ ] Changelog à jour

---

## 🎯 Focus Phase 2

L'enrichissement apporte :

1. **Flexibilité** : 20 types pour tous besoins
2. **Productivité** : Templates réutilisables
3. **Extensibilité** : API pour intégrations
4. **Communication** : Multi-canal natif
5. **Insights** : Analytics enrichis

Utilisez ces outils pour créer des sondages plus riches et obtenir des données plus qualitatives !

---

_Guide Administrateur Strapi - Phase 2 - Version 1.0_
