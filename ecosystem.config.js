module.exports = {
    apps: [
        {
            name: "civicpoll-backend",
            cwd: "./apps/backend",
            script: "pnpm",
            args: "start",
            instances: 2,
            exec_mode: "cluster",
            env: {
                NODE_ENV: "production",
                PORT: 1337,
            },
            error_file: "/opt/civicpoll/logs/backend-error.log",
            out_file: "/opt/civicpoll/logs/backend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "1G",
            // Auto restart on file changes (useful for development)
            watch: false,
            ignore_watch: ["node_modules", "public/uploads", ".tmp", "logs"],
            // Graceful shutdown
            kill_timeout: 5000,
            listen_timeout: 5000,
            // Environment-specific configurations
            env_development: {
                NODE_ENV: "development",
                watch: true,
            },
            env_staging: {
                NODE_ENV: "staging",
                instances: 1,
            },
        },
        {
            name: "civicpoll-frontend",
            cwd: "./apps/frontend",
            script: "pnpm",
            args: "start",
            instances: 2,
            exec_mode: "cluster",
            env: {
                NODE_ENV: "production",
                PORT: 3000,
            },
            error_file: "/opt/civicpoll/logs/frontend-error.log",
            out_file: "/opt/civicpoll/logs/frontend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "800M",
            // Auto restart on file changes
            watch: false,
            ignore_watch: ["node_modules", ".next", "logs"],
            // Graceful shutdown
            kill_timeout: 5000,
            listen_timeout: 5000,
            // Environment-specific configurations
            env_development: {
                NODE_ENV: "development",
                watch: true,
            },
            env_staging: {
                NODE_ENV: "staging",
                instances: 1,
            },
        },
    ],

    // Deploy configuration (optional)
    deploy: {
        production: {
            user: "deploy",
            host: "civicpoll.fr.smatflow.xyz",
            ref: "origin/main",
            repo: "**************:your-org/civicpoll.git",
            path: "/opt/civicpoll",
            "pre-deploy-local": "",
            "post-deploy":
                "pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.js --env production",
            "pre-setup": "",
        },
    },
};
