# CivicPoll Phase 0: Monorepo Development Plan

## ⚠️ IMPORTANT: Development Best Practices

**Before implementing any feature or making any changes:**

1. **ALWAYS** check the current project structure and existing files
2. **ANALYZE** the global context and current implementation status
3. **UNDERSTAND** existing patterns, conventions, and architecture decisions
4. **VERIFY** dependencies and configurations already in place
5. **NEVER** assume the state of the project - always investigate first
6. **MAINTAIN** a `PHASE_0_STATUS.md` file to track progress (completed tasks, pending items, blockers, notes)

> The project has already been initialized with Next.js 15 and Strapi v5. This plan builds upon the existing structure.

### Status Tracking File Structure

Create and maintain a `PHASE_0_STATUS.md` file with the following structure:

```markdown
# Phase 0 Implementation Status

## Last Updated: [DATE]

## Completed Tasks

- [ ] Task description - Date completed
- [ ] Another task - Date completed

## In Progress

- [ ] Current task being worked on
    - Current status
    - Any blockers

## Pending Tasks

- [ ] Future task
- [ ] Another future task

## Notes & Decisions

- Important decisions made
- Technical notes
- Dependencies installed
```

## Executive Summary

This development plan outlines the implementation of Phase 0 for the CivicPoll platform using the existing monorepo architecture with **pnpm**, **Turborepo**, **Strapi v5 with TypeScript** (backend), and **Next.js 15** (frontend). Phase 0 focuses on establishing secure foundations, GDPR compliance, and production-ready infrastructure without implementing user-facing features.

**Duration:** 4-6 weeks  
**Core Deliverable:** Secure, GDPR-compliant technical foundation ready for Phase 1 development  
**Note:** The project structure is already initialized - this plan focuses on implementation within the existing setup.

## Project Architecture

### Technology Stack

- **Package Manager:** pnpm (v10+)
- **Monorepo Tool:** Turborepo
- **Backend:** Strapi v5 with TypeScript (Node.js 18+)
- **Frontend:** Next.js 15 with TypeScript (React 18)
- **Database:** PostgreSQL 14 with PostGIS
- **Cache:** Redis
- **Web Server:** Nginx
- **Process Manager:** PM2
- **CI/CD:** GitLab CI
- **Monitoring:** Prometheus + Grafana
- **Container:** Docker

### Monorepo Structure

```
civicpoll/
├── apps/
│   ├── backend/          # Strapi v5 application
│   │   ├── config/
│   │   ├── src/
│   │   │   ├── api/
│   │   │   ├── plugins/
│   │   │   │   ├── gdpr/        # Custom GDPR plugin
│   │   │   │   └── audit-log/   # Audit trail plugin
│   │   │   └── extensions/
│   │   ├── public/
│   │   ├── database/
│   │   ├── tests/
│   │   └── package.json
│   │
│   └── frontend/         # Next.js 15 application
│       ├── src/
│       │   ├── app/
│       │   ├── components/
│       │   ├── lib/
│       │   └── styles/
│       ├── public/
│       ├── tests/
│       └── package.json
│
├── packages/
│   ├── eslint-config/    # Shared ESLint configuration
│   ├── typescript-config/ # Shared TypeScript configuration
│   ├── ui/               # Shared UI components (future)
│   └── utils/            # Shared utilities
│
├── infrastructure/
│   ├── docker/
│   │   ├── backend.Dockerfile
│   │   ├── frontend.Dockerfile
│   │   └── docker-compose.yml
│   ├── nginx/
│   ├── monitoring/
│   └── scripts/
│
├── .github/
├── .gitlab-ci.yml
├── turbo.json
├── pnpm-workspace.yaml
├── package.json
└── README.md
```

## Development Phases

### Phase 1: Project Configuration and Security Setup (Week 1)

> **Note:** Since the project is already initialized with Next.js 15 and Strapi v5, this phase focuses on configuration and security setup rather than initialization.

#### 1.1 Verify and Configure Existing Monorepo

```bash
# First, check the current project structure
ls -la
cat package.json
cat pnpm-workspace.yaml
cat turbo.json

# Verify Strapi and Next.js installations
cd apps/backend && pnpm list @strapi/strapi
cd ../frontend && pnpm list next

# Update turbo.json if needed to ensure proper pipeline configuration
```

#### 1.1.1 Expected turbo.json Configuration

```json
{
    "$schema": "https://turbo.build/schema.json",
    "globalDependencies": ["**/.env.*local"],
    "pipeline": {
        "build": {
            "dependsOn": ["^build"],
            "outputs": [".next/**", "!.next/cache/**", "build/**", "dist/**"]
        },
        "dev": {
            "cache": false,
            "persistent": true
        },
        "lint": {
            "dependsOn": ["^lint"]
        },
        "test": {
            "dependsOn": ["build"],
            "outputs": ["coverage/**"]
        },
        "typecheck": {
            "dependsOn": ["^typecheck"]
        }
    }
}
```

#### 1.2 Shared Packages Setup

```bash
# Create shared packages
mkdir -p packages/{eslint-config,typescript-config,utils}

# TypeScript config
cd packages/typescript-config
pnpm init
cat > tsconfig.base.json << EOF
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "allowJs": true,
    "noEmit": true
  }
}
EOF
```

#### 1.3 Backend (Strapi v5) Configuration

```bash
# First, check the existing Strapi setup
cd apps/backend
ls -la
cat package.json
cat tsconfig.json

# Verify TypeScript is configured
pnpm list typescript @types/node

# Add necessary dependencies for Phase 0
pnpm add pg @strapi/provider-email-sendgrid
pnpm add -D @types/node @types/pg

# Check existing environment configuration
if [ -f .env ]; then cat .env; fi
if [ -f .env.example ]; then cat .env.example; fi

# Create/Update environment configuration
cat > .env.example << EOF
# Server
HOST=0.0.0.0
PORT=1337
APP_KEYS=GENERATE_SECURE_KEYS_HERE
API_TOKEN_SALT=GENERATE_SALT_HERE
ADMIN_JWT_SECRET=GENERATE_SECRET_HERE
JWT_SECRET=GENERATE_SECRET_HERE

# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=civicpoll_fr
DATABASE_USERNAME=civicpoll_user
DATABASE_PASSWORD=SECURE_PASSWORD_HERE
DATABASE_SSL=true

# Email
EMAIL_PROVIDER=sendgrid
EMAIL_SENDGRID_API_KEY=YOUR_KEY_HERE
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# GDPR
GDPR_ENABLED=true
GDPR_DATA_RETENTION_DAYS=365
GDPR_ANONYMIZE_ON_DELETE=true

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Security
NODE_ENV=production
EOF
```

#### 1.4 Frontend (Next.js 15) Configuration

```bash
# First, check the existing Next.js setup
cd apps/frontend
ls -la
cat package.json
cat tsconfig.json
cat next.config.js || cat next.config.mjs

# Verify TypeScript and Next.js version
pnpm list next react react-dom typescript

# Add necessary dependencies for Phase 0
pnpm add axios @tanstack/react-query zod react-hook-form
pnpm add -D @types/node @types/react

# Check existing environment configuration
if [ -f .env.local ]; then cat .env.local; fi
if [ -f .env.local.example ]; then cat .env.local.example; fi

# Create/Update environment configuration
cat > .env.local.example << EOF
NEXT_PUBLIC_API_URL=http://localhost:1337
NEXT_PUBLIC_SITE_URL=https://civicpoll.fr.smatflow.xyz
EOF
```

### Phase 2: Security Implementation (Week 2)

#### 2.1 SSL/TLS Configuration

```nginx
# infrastructure/nginx/sites-available/civicpoll
server {
    listen 443 ssl http2;
    server_name civicpoll.fr.smatflow.xyz;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/civicpoll.fr.smatflow.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(self), microphone=(), camera=()" always;

    # CSP Header
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.smatflow.xyz;" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/m;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API
    location /api {
        limit_req zone=api_limit burst=20 nodelay;

        proxy_pass http://localhost:1337;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Auth endpoints with stricter rate limiting
    location ~ ^/api/auth {
        limit_req zone=auth_limit burst=10 nodelay;

        proxy_pass http://localhost:1337;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 2.2 Strapi Security Configuration

```typescript
// apps/backend/config/server.ts
import { Strapi } from "@strapi/strapi";

export default ({ env }: { env: any }) => ({
    host: env("HOST", "0.0.0.0"),
    port: env.int("PORT", 1337),
    app: {
        keys: env.array("APP_KEYS"),
    },
    webhooks: {
        populateRelations: env.bool("WEBHOOKS_POPULATE_RELATIONS", false),
    },
});

// apps/backend/config/middlewares.ts
export default [
    "strapi::errors",
    {
        name: "strapi::security",
        config: {
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    "connect-src": ["'self'", "https:"],
                    "img-src": ["'self'", "data:", "blob:", "https:"],
                    "media-src": ["'self'", "data:", "blob:", "https:"],
                    upgradeInsecureRequests: null,
                },
            },
            hsts: {
                enabled: true,
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true,
            },
        },
    },
    {
        name: "strapi::cors",
        config: {
            origin: ["https://civicpoll.fr.smatflow.xyz"],
            credentials: true,
        },
    },
    "strapi::poweredBy",
    "strapi::logger",
    "strapi::query",
    {
        name: "strapi::body",
        config: {
            jsonLimit: "10mb",
            textLimit: "10mb",
        },
    },
    "strapi::session",
    "strapi::favicon",
    "strapi::public",
];
```

### Phase 3: GDPR Implementation (Week 3)

> **Important:** Before implementing any GDPR features, verify the existing plugin structure and content types in the Strapi backend.

#### 3.1 GDPR Plugin Development

```typescript
// First, check if plugins directory exists
// cd apps/backend/src && ls -la
// If not, create: mkdir -p plugins/gdpr/server

// apps/backend/src/plugins/gdpr/server/bootstrap.ts
import { Strapi } from "@strapi/strapi";

export default async ({ strapi }: { strapi: Strapi }) => {
    // Register GDPR permissions
    const actions = ["export-data", "delete-data", "manage-consent", "view-audit-log"];

    await Promise.all(
        actions.map((action) =>
            strapi.admin.services.permission.actionProvider.register({
                section: "plugins",
                displayName: action,
                uid: `plugin::gdpr.${action}`,
                subCategory: "gdpr",
                pluginName: "gdpr",
            }),
        ),
    );
};

// apps/backend/src/plugins/gdpr/server/controllers/gdpr.ts
import { Context } from "koa";

export default {
    async exportUserData(ctx: Context) {
        const { userId } = ctx.params;
        const { user } = ctx.state;

        // Verify user can export their own data or has admin rights
        if (user.id !== parseInt(userId) && !user.isAdmin) {
            return ctx.forbidden("You can only export your own data");
        }

        try {
            // Collect all user data
            const userData = await strapi.db.query("plugin::users-permissions.user").findOne({
                where: { id: userId },
                populate: {
                    responses: true,
                    sessions: {
                        populate: {
                            surveys: true,
                        },
                    },
                    notifications: true,
                    consents: true,
                },
            });

            // Log the export event
            await strapi
                .plugin("gdpr")
                .service("auditLog")
                .create({
                    event: "DATA_EXPORT",
                    userId,
                    performedBy: user.id,
                    ipAddress: ctx.request.ip,
                    userAgent: ctx.request.headers["user-agent"],
                    details: { format: "json" },
                });

            // Return formatted data
            return {
                exportDate: new Date().toISOString(),
                user: {
                    profile: {
                        id: userData.id,
                        email: userData.email,
                        username: userData.username,
                        createdAt: userData.createdAt,
                    },
                    responses: userData.responses,
                    sessions: userData.sessions,
                    notifications: userData.notifications,
                    consents: userData.consents,
                },
            };
        } catch (error) {
            strapi.log.error("GDPR export error:", error);
            return ctx.internalServerError("Failed to export user data");
        }
    },

    async deleteUserData(ctx: Context) {
        const { userId } = ctx.params;
        const { user } = ctx.state;

        // Verify user can delete their own data or has admin rights
        if (user.id !== parseInt(userId) && !user.isAdmin) {
            return ctx.forbidden("You can only delete your own data");
        }

        try {
            // Begin transaction
            const knex = strapi.db.connection;
            await knex.transaction(async (trx) => {
                // Anonymize user data
                await trx("users-permissions_user")
                    .where("id", userId)
                    .update({
                        email: `deleted_${userId}_${Date.now()}@anonymous.local`,
                        username: `deleted_user_${userId}`,
                        firstName: "Deleted",
                        lastName: "User",
                        phoneNumber: null,
                        dateOfBirth: null,
                        blocked: true,
                    });

                // Anonymize but keep responses for statistical purposes
                await trx("responses").where("user", userId).update({
                    user: null,
                    anonymized: true,
                    anonymizedAt: new Date(),
                });

                // Delete sensitive data
                await trx("sessions").where("user", userId).del();
                await trx("notifications").where("user", userId).del();
                await trx("consents").where("user", userId).del();
            });

            // Log the deletion event
            await strapi
                .plugin("gdpr")
                .service("auditLog")
                .create({
                    event: "DATA_DELETION",
                    userId,
                    performedBy: user.id,
                    ipAddress: ctx.request.ip,
                    userAgent: ctx.request.headers["user-agent"],
                    details: { anonymized: true },
                });

            return { message: "User data has been anonymized and deleted" };
        } catch (error) {
            strapi.log.error("GDPR deletion error:", error);
            return ctx.internalServerError("Failed to delete user data");
        }
    },

    async getConsents(ctx: Context) {
        const { userId } = ctx.params;
        const { user } = ctx.state;

        if (user.id !== parseInt(userId) && !user.isAdmin) {
            return ctx.forbidden("You can only view your own consents");
        }

        const consents = await strapi.db.query("api::consent.consent").findMany({
            where: { user: userId },
            orderBy: { createdAt: "desc" },
        });

        return { consents };
    },

    async updateConsent(ctx: Context) {
        const { userId } = ctx.params;
        const { type, granted } = ctx.request.body;
        const { user } = ctx.state;

        if (user.id !== parseInt(userId)) {
            return ctx.forbidden("You can only update your own consents");
        }

        const consent = await strapi.db.query("api::consent.consent").create({
            data: {
                user: userId,
                type,
                granted,
                ipAddress: ctx.request.ip,
                userAgent: ctx.request.headers["user-agent"],
            },
        });

        // Log consent update
        await strapi.plugin("gdpr").service("auditLog").create({
            event: "CONSENT_UPDATE",
            userId,
            performedBy: user.id,
            ipAddress: ctx.request.ip,
            userAgent: ctx.request.headers["user-agent"],
            details: { type, granted },
        });

        return { consent };
    },
};
```

#### 3.2 Audit Log Implementation

```typescript
// apps/backend/src/plugins/audit-log/server/services/audit-log.ts
import { Strapi } from "@strapi/strapi";

interface AuditLogData {
    event: string;
    userId: number;
    performedBy: number;
    ipAddress: string;
    userAgent: string;
    details?: Record<string, any>;
}

export default ({ strapi }: { strapi: Strapi }) => ({
    async create(data: AuditLogData) {
        const entry = await strapi.db.query("plugin::audit-log.audit-log").create({
            data: {
                event: data.event,
                userId: data.userId,
                performedBy: data.performedBy,
                ipAddress: data.ipAddress,
                userAgent: data.userAgent,
                timestamp: new Date(),
                details: JSON.stringify(data.details || {}),
            },
        });

        // Check retention policy
        await this.enforceRetention();

        return entry;
    },

    async enforceRetention() {
        const retentionDays = strapi.config.get("plugin.gdpr.retentionDays", 365);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        await strapi.db.query("plugin::audit-log.audit-log").deleteMany({
            where: {
                timestamp: {
                    $lt: cutoffDate,
                },
            },
        });
    },

    async search(filters: Record<string, any>) {
        return strapi.db.query("plugin::audit-log.audit-log").findMany({
            where: filters,
            orderBy: { timestamp: "desc" },
            limit: 100,
        });
    },
});
```

### Phase 4: CI/CD & DevOps (Week 4)

#### 4.1 GitLab CI Configuration

```yaml
# .gitlab-ci.yml
stages:
    - install
    - lint
    - test
    - build
    - security
    - deploy

variables:
    NODE_VERSION: "18"
    PNPM_VERSION: "8"

# Cache configuration
.cache_config: &cache_config
    cache:
        key:
            files:
                - pnpm-lock.yaml
        paths:
            - .pnpm-store

# Install dependencies
install:
    stage: install
    image: node:$NODE_VERSION
    <<: *cache_config
    before_script:
        - corepack enable
        - corepack prepare pnpm@$PNPM_VERSION --activate
        - pnpm config set store-dir .pnpm-store
    script:
        - pnpm install --frozen-lockfile
    artifacts:
        paths:
            - node_modules/
            - apps/*/node_modules/
            - packages/*/node_modules/
        expire_in: 1 hour

# Linting
lint:
    stage: lint
    image: node:$NODE_VERSION
    needs: ["install"]
    script:
        - pnpm run lint

# Type checking
typecheck:
    stage: lint
    image: node:$NODE_VERSION
    needs: ["install"]
    script:
        - pnpm run typecheck

# Unit tests
test:unit:
    stage: test
    image: node:$NODE_VERSION
    needs: ["install"]
    services:
        - postgres:14-alpine
        - redis:7-alpine
    variables:
        POSTGRES_DB: test_db
        POSTGRES_USER: test_user
        POSTGRES_PASSWORD: test_pass
        DATABASE_HOST: postgres
        REDIS_HOST: redis
    script:
        - pnpm run test:unit -- --coverage
    coverage: '/Coverage: \d+\.\d+%/'
    artifacts:
        reports:
            coverage_report:
                coverage_format: cobertura
                path: coverage/cobertura-coverage.xml

# Integration tests
test:integration:
    stage: test
    image: node:$NODE_VERSION
    needs: ["install"]
    services:
        - postgres:14-alpine
        - redis:7-alpine
    variables:
        POSTGRES_DB: test_db
        POSTGRES_USER: test_user
        POSTGRES_PASSWORD: test_pass
        DATABASE_HOST: postgres
        REDIS_HOST: redis
    script:
        - pnpm run test:integration

# Security scan
security:dependencies:
    stage: security
    image: node:$NODE_VERSION
    needs: ["install"]
    script:
        - pnpm audit --production --audit-level=high
    allow_failure: true

security:sast:
    stage: security
    image: node:$NODE_VERSION
    needs: ["install"]
    script:
        - pnpm add -D @microsoft/eslint-plugin-sdl
        - pnpm run lint:security
    allow_failure: true

# Build applications
build:backend:
    stage: build
    image: node:$NODE_VERSION
    needs: ["install", "test:unit"]
    script:
        - cd apps/backend
        - pnpm run build
    artifacts:
        paths:
            - apps/backend/build/
        expire_in: 1 week

build:frontend:
    stage: build
    image: node:$NODE_VERSION
    needs: ["install", "test:unit"]
    script:
        - cd apps/frontend
        - pnpm run build
    artifacts:
        paths:
            - apps/frontend/.next/
        expire_in: 1 week

# Docker build
build:docker:
    stage: build
    image: docker:latest
    services:
        - docker:dind
    needs: ["build:backend", "build:frontend"]
    script:
        - docker build -f infrastructure/docker/backend.Dockerfile -t civicpoll-backend:$CI_COMMIT_SHA .
        - docker build -f infrastructure/docker/frontend.Dockerfile -t civicpoll-frontend:$CI_COMMIT_SHA .
    only:
        - main
        - develop

# Deploy to production
deploy:production:
    stage: deploy
    image: alpine:latest
    needs: ["build:docker"]
    before_script:
        - apk add --no-cache openssh-client rsync
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | ssh-add -
        - mkdir -p ~/.ssh
        - chmod 700 ~/.ssh
        - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
    script:
        - |
            ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
              cd /opt/civicpoll
              git pull origin main
              pnpm install --frozen-lockfile
              pnpm run build
              
              # Backup current deployment
              ./infrastructure/scripts/backup.sh
              
              # Deploy with zero downtime
              pm2 reload ecosystem.config.js --update-env
              
              # Run migrations
              cd apps/backend
              pnpm run strapi migration:run
              
              # Health check
              sleep 10
              curl -f https://civicpoll.fr.smatflow.xyz/api/health || exit 1
            EOF
    only:
        - main
    environment:
        name: production
        url: https://civicpoll.fr.smatflow.xyz
```

#### 4.2 PM2 Ecosystem Configuration

```javascript
// ecosystem.config.js
module.exports = {
    apps: [
        {
            name: "civicpoll-backend",
            cwd: "./apps/backend",
            script: "pnpm",
            args: "start",
            instances: 2,
            exec_mode: "cluster",
            env: {
                NODE_ENV: "production",
                PORT: 1337,
            },
            error_file: "/opt/civicpoll/logs/backend-error.log",
            out_file: "/opt/civicpoll/logs/backend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "1G",
        },
        {
            name: "civicpoll-frontend",
            cwd: "./apps/frontend",
            script: "pnpm",
            args: "start",
            instances: 2,
            exec_mode: "cluster",
            env: {
                NODE_ENV: "production",
                PORT: 3000,
            },
            error_file: "/opt/civicpoll/logs/frontend-error.log",
            out_file: "/opt/civicpoll/logs/frontend-out.log",
            log_date_format: "YYYY-MM-DD HH:mm:ss Z",
            max_memory_restart: "800M",
        },
    ],
};
```

### Phase 5: Monitoring & Backup (Week 5)

#### 5.1 Monitoring Setup

```yaml
# infrastructure/monitoring/prometheus.yml
global:
    scrape_interval: 15s
    evaluation_interval: 15s

alerting:
    alertmanagers:
        - static_configs:
              - targets: ["localhost:9093"]

rule_files:
    - "alerts/*.yml"

scrape_configs:
    - job_name: "civicpoll-backend"
      static_configs:
          - targets: ["localhost:1337"]
      metrics_path: "/api/metrics"
      bearer_token: "YOUR_METRICS_TOKEN"

    - job_name: "civicpoll-frontend"
      static_configs:
          - targets: ["localhost:3000"]
      metrics_path: "/api/metrics"

    - job_name: "node"
      static_configs:
          - targets: ["localhost:9100"]

    - job_name: "postgres"
      static_configs:
          - targets: ["localhost:9187"]

    - job_name: "redis"
      static_configs:
          - targets: ["localhost:9121"]

    - job_name: "nginx"
      static_configs:
          - targets: ["localhost:9113"]
```

#### 5.2 Alert Rules

```yaml
# infrastructure/monitoring/alerts/civicpoll.yml
groups:
    - name: civicpoll
      interval: 30s
      rules:
          # High error rate
          - alert: HighErrorRate
            expr: |
                (
                  rate(http_requests_total{status=~"5.."}[5m]) * 100
                  / rate(http_requests_total[5m])
                ) > 5
            for: 5m
            labels:
                severity: critical
                service: civicpoll
            annotations:
                summary: "High error rate detected"
                description: "Error rate is {{ $value }}% for the last 5 minutes"

          # API response time
          - alert: SlowAPIResponse
            expr: |
                histogram_quantile(0.95,
                  rate(http_request_duration_seconds_bucket[5m])
                ) > 1
            for: 10m
            labels:
                severity: warning
                service: civicpoll-api
            annotations:
                summary: "Slow API response times"
                description: "95th percentile response time is {{ $value }}s"

          # Database connection
          - alert: DatabaseDown
            expr: up{job="postgres"} == 0
            for: 1m
            labels:
                severity: critical
                service: postgres
            annotations:
                summary: "PostgreSQL database is down"
                description: "PostgreSQL has been down for more than 1 minute"

          # Disk space
          - alert: DiskSpaceLow
            expr: |
                (
                  node_filesystem_avail_bytes{mountpoint="/"}
                  / node_filesystem_size_bytes{mountpoint="/"}
                ) * 100 < 10
            for: 5m
            labels:
                severity: warning
            annotations:
                summary: "Low disk space"
                description: "Only {{ $value }}% disk space remaining"

          # Memory usage
          - alert: HighMemoryUsage
            expr: |
                (
                  1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)
                ) * 100 > 90
            for: 10m
            labels:
                severity: warning
            annotations:
                summary: "High memory usage"
                description: "Memory usage is {{ $value }}%"

          # SSL certificate expiry
          - alert: SSLCertificateExpiringSoon
            expr: |
                (
                  probe_ssl_earliest_cert_expiry - time()
                ) / 86400 < 30
            for: 1h
            labels:
                severity: warning
            annotations:
                summary: "SSL certificate expiring soon"
                description: "SSL certificate expires in {{ $value }} days"

          # GDPR compliance
          - alert: GDPRDataRetentionViolation
            expr: |
                count(
                  time() - audit_log_timestamp > 365 * 86400
                ) > 0
            for: 1h
            labels:
                severity: critical
                compliance: gdpr
            annotations:
                summary: "GDPR data retention violation"
                description: "Found {{ $value }} audit logs older than 365 days"
```

#### 5.3 Backup Strategy

```bash
#!/bin/bash
# infrastructure/scripts/backup.sh

set -euo pipefail

# Configuration
BACKUP_ROOT="/opt/civicpoll/backups"
S3_BUCKET="s3://civicpoll-backups/fr"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/$DATE"

# Ensure backup directory exists
mkdir -p "$BACKUP_DIR"

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_ROOT/backup.log"
}

# Function to handle errors
handle_error() {
    log "ERROR: Backup failed at line $1"
    # Send alert
    curl -X POST "$ALERTMANAGER_URL/api/v1/alerts" \
        -H "Content-Type: application/json" \
        -d '[{
            "labels": {
                "alertname": "BackupFailed",
                "severity": "critical",
                "service": "civicpoll"
            },
            "annotations": {
                "summary": "Backup process failed",
                "description": "The backup process failed at '"$(date)"'"
            }
        }]'
    exit 1
}

trap 'handle_error $LINENO' ERR

log "Starting backup process"

# 1. Database backup
log "Backing up PostgreSQL database"
PGPASSWORD="$DB_PASSWORD" pg_dump \
    -h localhost \
    -U civicpoll_user \
    -d civicpoll_fr \
    --no-owner \
    --no-acl \
    --clean \
    --if-exists \
    | gzip -9 > "$BACKUP_DIR/database.sql.gz"

# 2. Strapi media files
log "Backing up Strapi media files"
tar -czf "$BACKUP_DIR/media.tar.gz" \
    -C /opt/civicpoll/apps/backend \
    public/uploads

# 3. Configuration files
log "Backing up configuration files"
tar -czf "$BACKUP_DIR/config.tar.gz" \
    -C /opt/civicpoll \
    --exclude=node_modules \
    --exclude=.next \
    --exclude=build \
    --exclude=dist \
    apps/backend/config \
    apps/backend/.env \
    apps/frontend/.env.local \
    infrastructure/nginx \
    ecosystem.config.js

# 4. Audit logs export (GDPR compliance)
log "Exporting audit logs"
PGPASSWORD="$DB_PASSWORD" psql \
    -h localhost \
    -U civicpoll_user \
    -d civicpoll_fr \
    -c "\COPY (SELECT * FROM audit_logs WHERE timestamp >= NOW() - INTERVAL '7 days') TO '$BACKUP_DIR/audit_logs.csv' CSV HEADER"

# 5. Create backup manifest
cat > "$BACKUP_DIR/manifest.json" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "version": "$(cd /opt/civicpoll && git rev-parse HEAD)",
    "type": "full",
    "components": {
        "database": "database.sql.gz",
        "media": "media.tar.gz",
        "config": "config.tar.gz",
        "audit_logs": "audit_logs.csv"
    },
    "checksums": {
        "database": "$(sha256sum $BACKUP_DIR/database.sql.gz | cut -d' ' -f1)",
        "media": "$(sha256sum $BACKUP_DIR/media.tar.gz | cut -d' ' -f1)",
        "config": "$(sha256sum $BACKUP_DIR/config.tar.gz | cut -d' ' -f1)",
        "audit_logs": "$(sha256sum $BACKUP_DIR/audit_logs.csv | cut -d' ' -f1)"
    }
}
EOF

# 6. Create encrypted archive
log "Creating encrypted backup archive"
tar -czf - -C "$BACKUP_ROOT" "$DATE" | \
    openssl enc -aes-256-cbc -salt -pbkdf2 -pass pass:"$BACKUP_ENCRYPTION_KEY" \
    > "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc"

# 7. Upload to S3 (if configured)
if [ -n "${S3_BUCKET:-}" ]; then
    log "Uploading backup to S3"
    aws s3 cp "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" \
        "$S3_BUCKET/civicpoll_backup_$DATE.tar.gz.enc" \
        --storage-class STANDARD_IA
fi

# 8. Cleanup old backups
log "Cleaning up old backups"
find "$BACKUP_ROOT" -name "civicpoll_backup_*.tar.gz.enc" -mtime +$RETENTION_DAYS -delete

# 9. Test backup integrity
log "Testing backup integrity"
openssl enc -aes-256-cbc -d -salt -pbkdf2 -pass pass:"$BACKUP_ENCRYPTION_KEY" \
    -in "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc" | \
    tar -tzf - > /dev/null

# 10. Update metrics
curl -X POST http://localhost:1337/api/metrics/backup \
    -H "Authorization: Bearer $METRICS_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "status": "success",
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "size": '$(stat -c%s "$BACKUP_ROOT/civicpoll_backup_$DATE.tar.gz.enc")',
        "duration": '$SECONDS'
    }'

# Cleanup temporary directory
rm -rf "$BACKUP_DIR"

log "Backup completed successfully"
```

### Phase 6: Testing & Validation (Week 6)

#### 6.1 Security Testing Checklist

```markdown
## Security Validation Checklist

### SSL/TLS Configuration

- [ ] SSL Labs score: A or A+
- [ ] TLS 1.2 and 1.3 only
- [ ] Strong cipher suites
- [ ] HSTS enabled with preload
- [ ] Certificate renewal automated

### Security Headers

- [ ] Content-Security-Policy configured
- [ ] X-Frame-Options: SAMEORIGIN
- [ ] X-Content-Type-Options: nosniff
- [ ] X-XSS-Protection enabled
- [ ] Referrer-Policy configured
- [ ] Permissions-Policy set

### Authentication & Authorization

- [ ] Strong password policy enforced
- [ ] Account lockout after failed attempts
- [ ] Session timeout configured
- [ ] JWT tokens expire appropriately
- [ ] Rate limiting on auth endpoints

### GDPR Compliance

- [ ] Data export functionality tested
- [ ] Data deletion/anonymization verified
- [ ] Audit logs capturing all events
- [ ] Consent management functional
- [ ] Data retention policy enforced
- [ ] Privacy policy accessible

### Infrastructure Security

- [ ] Firewall rules configured
- [ ] SSH key-based authentication only
- [ ] Fail2ban configured
- [ ] Database encrypted at rest
- [ ] Backups encrypted
- [ ] Secrets properly managed

### Application Security

- [ ] Input validation on all endpoints
- [ ] SQL injection prevention verified
- [ ] XSS protection tested
- [ ] CSRF tokens implemented
- [ ] File upload restrictions
- [ ] Error messages don't leak info
```

#### 6.2 Performance Testing

```bash
#!/bin/bash
# infrastructure/scripts/performance-test.sh

# Load testing with k6
cat > load-test.js << 'EOF'
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 },
    { duration: '5m', target: 100 },
    { duration: '2m', target: 200 },
    { duration: '5m', target: 200 },
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],
    http_req_failed: ['rate<0.1'],
  },
};

export default function() {
  // Test API health endpoint
  let health = http.get('https://civicpoll.fr.smatflow.xyz/api/health');
  check(health, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 200ms': (r) => r.timings.duration < 200,
  });

  // Test homepage
  let homepage = http.get('https://civicpoll.fr.smatflow.xyz');
  check(homepage, {
    'homepage status is 200': (r) => r.status === 200,
    'homepage response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
EOF

k6 run load-test.js
```

#### 6.3 GDPR Compliance Testing

```typescript
// apps/backend/tests/gdpr/compliance.test.ts
import { describe, it, expect, beforeAll, afterAll } from "@jest/globals";
import request from "supertest";
import { setupStrapi, teardownStrapi } from "../helpers";

describe("GDPR Compliance", () => {
    let app;
    let authToken;
    let testUserId;

    beforeAll(async () => {
        app = await setupStrapi();

        // Create test user
        const response = await request(app.server).post("/api/auth/local/register").send({
            username: "gdprtest",
            email: "<EMAIL>",
            password: "Test123!@#",
        });

        authToken = response.body.jwt;
        testUserId = response.body.user.id;
    });

    afterAll(async () => {
        await teardownStrapi();
    });

    describe("Data Export", () => {
        it("should allow users to export their data", async () => {
            const response = await request(app.server)
                .get(`/api/gdpr/export/${testUserId}`)
                .set("Authorization", `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).toHaveProperty("exportDate");
            expect(response.body).toHaveProperty("user");
            expect(response.body.user).toHaveProperty("profile");
            expect(response.body.user.profile.email).toBe("<EMAIL>");
        });

        it("should not allow exporting other users data", async () => {
            await request(app.server)
                .get("/api/gdpr/export/9999")
                .set("Authorization", `Bearer ${authToken}`)
                .expect(403);
        });

        it("should log export events", async () => {
            await request(app.server)
                .get(`/api/gdpr/export/${testUserId}`)
                .set("Authorization", `Bearer ${authToken}`);

            const logs = await request(app.server)
                .get("/api/audit-logs")
                .query({ event: "DATA_EXPORT", userId: testUserId })
                .set("Authorization", `Bearer ${authToken}`);

            expect(logs.body.length).toBeGreaterThan(0);
            expect(logs.body[0].event).toBe("DATA_EXPORT");
        });
    });

    describe("Data Deletion", () => {
        it("should anonymize user data on deletion", async () => {
            // Create a new user for deletion test
            const newUser = await request(app.server).post("/api/auth/local/register").send({
                username: "deletetest",
                email: "<EMAIL>",
                password: "Test123!@#",
            });

            const deleteToken = newUser.body.jwt;
            const deleteUserId = newUser.body.user.id;

            // Delete user data
            await request(app.server)
                .delete(`/api/gdpr/delete/${deleteUserId}`)
                .set("Authorization", `Bearer ${deleteToken}`)
                .expect(200);

            // Verify anonymization
            const deletedUser = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({ where: { id: deleteUserId } });

            expect(deletedUser.email).toMatch(/deleted_\d+_\d+@anonymous\.local/);
            expect(deletedUser.username).toMatch(/deleted_user_\d+/);
            expect(deletedUser.blocked).toBe(true);
        });
    });

    describe("Consent Management", () => {
        it("should track user consents", async () => {
            const response = await request(app.server)
                .post(`/api/gdpr/consent/${testUserId}`)
                .set("Authorization", `Bearer ${authToken}`)
                .send({
                    type: "marketing",
                    granted: true,
                })
                .expect(200);

            expect(response.body.consent).toHaveProperty("type", "marketing");
            expect(response.body.consent).toHaveProperty("granted", true);
        });

        it("should retrieve consent history", async () => {
            const response = await request(app.server)
                .get(`/api/gdpr/consents/${testUserId}`)
                .set("Authorization", `Bearer ${authToken}`)
                .expect(200);

            expect(Array.isArray(response.body.consents)).toBe(true);
        });
    });

    describe("Data Retention", () => {
        it("should enforce retention policy on audit logs", async () => {
            // This would be tested with a scheduled job
            // For now, verify the service exists
            expect(strapi.plugin("gdpr").service("auditLog").enforceRetention).toBeDefined();
        });
    });
});
```

## Development Best Practices Reminder

Throughout all phases of development:

1. **Always Check First**: Before writing any code, check existing implementations
2. **Understand the Context**: Read related files and understand the patterns
3. **TypeScript First**: All code should be written in TypeScript with proper types
4. **Test Everything**: Write tests for all new functionality
5. **Document Changes**: Update documentation as you implement features
6. **Track Progress**: Update the `PHASE_0_STATUS.md` file after every significant change or task completion
7. **Regular Status Updates**: At the end of each day, review and update the status file with progress, blockers, and next steps

## Validation Criteria

### Pre-Production Checklist

1. **Security**

    - [ ] SSL Labs Score: A minimum
    - [ ] OWASP ZAP scan: No critical vulnerabilities
    - [ ] Security headers all present
    - [ ] Rate limiting functional
    - [ ] WAF blocking malicious requests

2. **GDPR Compliance**

    - [ ] Data export tested and functional
    - [ ] Data deletion with anonymization verified
    - [ ] Audit trail capturing all required events
    - [ ] Consent management operational
    - [ ] Privacy policy and terms accessible

3. **Infrastructure**

    - [ ] All services running and healthy
    - [ ] Monitoring dashboards operational
    - [ ] Alerts configured and tested
    - [ ] Backup and restore verified
    - [ ] CI/CD pipeline fully functional

4. **Performance**

    - [ ] Response times within targets
    - [ ] Load tests passed
    - [ ] Database queries optimized
    - [ ] Caching strategies implemented

5. **Documentation**
    - [ ] Technical architecture documented
    - [ ] API documentation generated
    - [ ] Runbooks created
    - [ ] Security procedures documented
    - [ ] Disaster recovery plan in place

## Timeline Summary

- **Week 1**: Project initialization and monorepo setup
- **Week 2**: Security implementation and hardening
- **Week 3**: GDPR features and compliance
- **Week 4**: CI/CD pipeline and automation
- **Week 5**: Monitoring, alerting, and backup systems
- **Week 6**: Testing, validation, and documentation

## Next Steps (Phase 1 Preview)

After Phase 0 validation, Phase 1 will introduce:

- User authentication with SMATFLOW SSO
- Survey creation and management APIs
- Geolocation integration with PostGIS
- Basic survey response collection
- Initial analytics dashboard
- Public survey interface

## Conclusion

This Phase 0 development plan establishes a rock-solid foundation for the CivicPoll platform. By focusing exclusively on infrastructure, security, and compliance, we ensure that all future development can proceed with confidence in the platform's reliability, security, and GDPR compliance.

The monorepo structure with pnpm and Turborepo provides excellent developer experience and build performance, while the comprehensive security measures and monitoring ensure production readiness from day one.
