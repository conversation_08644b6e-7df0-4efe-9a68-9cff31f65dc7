{"name": "civicpoll", "version": "0.1.0", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "test:watch": "turbo test:watch", "test:coverage": "turbo test:coverage", "format": "prettier --write \"**/*.{ts,tsx,js,md}\""}, "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "prettier": "^3.5.3", "ts-jest": "^29.1.0", "turbo": "latest"}, "packageManager": "pnpm@10.12.1", "engines": {"node": ">=20"}}