version: "3.8"

services:
    # PostgreSQL Database with PostGIS
    postgres:
        image: postgis/postgis:14-3.2
        container_name: civicpoll-postgres
        environment:
            POSTGRES_DB: civicpoll_fr
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: foh4ieRo
            POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
        volumes:
            - postgres_data:/var/lib/postgresql/data
            # - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
        ports:
            - "5432:5432"
        networks:
            - civicpoll-network
        restart: unless-stopped
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U civicpoll_user -d civicpoll_fr"]
            interval: 30s
            timeout: 10s
            retries: 3

    # Redis for caching and sessions
    redis:
        image: redis:7-alpine
        container_name: civicpoll-redis
        command: redis-server --requirepass ${REDIS_PASSWORD}
        volumes:
            - redis_data:/data
        ports:
            - "6379:6379"
        networks:
            - civicpoll-network
        restart: unless-stopped
        healthcheck:
            test: ["CMD", "redis-cli", "ping"]
            interval: 30s
            timeout: 10s
            retries: 3

    # Strapi Backend
    backend:
        build:
            context: ../..
            dockerfile: infrastructure/docker/backend.Dockerfile
        container_name: civicpoll-backend
        restart: unless-stopped
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        environment:
            NODE_ENV: ${NODE_ENV:-production}
            DATABASE_CLIENT: postgres
            DATABASE_HOST: postgres
            DATABASE_PORT: 5432
            DATABASE_NAME: ${DATABASE_NAME:-civicpoll_fr}
            DATABASE_USERNAME: ${DATABASE_USERNAME:-civicpoll_user}
            DATABASE_PASSWORD: ${DATABASE_PASSWORD:-secure_password}
            DATABASE_SSL: "false"
            REDIS_HOST: redis
            REDIS_PORT: 6379
            REDIS_PASSWORD: ${REDIS_PASSWORD:-}
            APP_KEYS: ${APP_KEYS}
            API_TOKEN_SALT: ${API_TOKEN_SALT}
            ADMIN_JWT_SECRET: ${ADMIN_JWT_SECRET}
            JWT_SECRET: ${JWT_SECRET}
            EMAIL_PROVIDER: ${EMAIL_PROVIDER:-sendgrid}
            EMAIL_SENDGRID_API_KEY: ${EMAIL_SENDGRID_API_KEY}
            EMAIL_FROM: ${EMAIL_FROM:-<EMAIL>}
            EMAIL_REPLY_TO: ${EMAIL_REPLY_TO:-<EMAIL>}
        ports:
            - "1337:1337"
        volumes:
            - strapi_uploads:/app/public/uploads
        networks:
            - civicpoll-network

    # Next.js Frontend
    frontend:
        build:
            context: ../..
            dockerfile: infrastructure/docker/frontend.Dockerfile
            args:
                NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:1337}
                NEXT_PUBLIC_FRONTEND_URL: ${NEXT_PUBLIC_FRONTEND_URL:-http://localhost:3000}
        container_name: civicpoll-frontend
        restart: unless-stopped
        depends_on:
            - backend
        environment:
            NODE_ENV: ${NODE_ENV:-production}
        ports:
            - "3000:3000"
        networks:
            - civicpoll-network

    # Nginx Reverse Proxy (Optional - for local development with SSL)
    nginx:
        image: nginx:alpine
        container_name: civicpoll-nginx
        restart: unless-stopped
        depends_on:
            - frontend
            - backend
        ports:
            - "80:80"
            - "443:443"
        volumes:
            - ../nginx/civicpoll.conf:/etc/nginx/conf.d/default.conf:ro
            - nginx_certs:/etc/letsencrypt
            - nginx_logs:/var/log/nginx
        networks:
            - civicpoll-network

volumes:
    postgres_data:
        driver: local
    redis_data:
        driver: local
    strapi_uploads:
        driver: local
    nginx_certs:
        driver: local
    nginx_logs:
        driver: local

networks:
    civicpoll-network:
        driver: bridge
