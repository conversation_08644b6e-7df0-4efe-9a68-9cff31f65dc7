# Phase 0 Enhancement Recommendations

Based on the comprehensive development plan analysis, these enhancements should be added to the current Phase 0 implementation:

## Priority 1: Critical Security & Infrastructure (Must Have)

### 1. Server Hardening Script

```bash
#!/bin/bash
# Server hardening for Ubuntu 22.04 LTS

# System updates
apt update && apt upgrade -y

# Install essential security tools
apt install -y ufw fail2ban unattended-upgrades

# Configure UFW firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 1337/tcp  # Strapi (internal only)
ufw allow 3000/tcp  # Next.js (internal only)
ufw --force enable

# Configure fail2ban
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = 22
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
EOF

systemctl enable fail2ban
systemctl start fail2ban

# Disable root login
sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
systemctl restart sshd

# Create application user
useradd -m -s /bin/bash civicpoll
usermod -aG sudo civicpoll

# Set up automatic security updates
dpkg-reconfigure -plow unattended-upgrades
```

### 2. ModSecurity WAF Configuration

```nginx
# Add to nginx configuration
load_module modules/ngx_http_modsecurity_module.so;

http {
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsecurity/modsecurity.conf;

    # OWASP Core Rule Set
    include /etc/nginx/modsecurity/crs/crs-setup.conf;
    include /etc/nginx/modsecurity/crs/rules/*.conf;
}
```

### 3. PostgreSQL Connection Pooling

```javascript
// apps/backend/config/database.ts enhancement
export default ({ env }) => ({
    connection: {
        client: "postgres",
        connection: {
            host: env("DATABASE_HOST", "localhost"),
            port: env.int("DATABASE_PORT", 5432),
            database: env("DATABASE_NAME", "civicpoll_fr"),
            user: env("DATABASE_USERNAME", "civicpoll_user"),
            password: env("DATABASE_PASSWORD"),
            ssl: env.bool("DATABASE_SSL", true),
        },
        pool: {
            min: env.int("DATABASE_POOL_MIN", 2),
            max: env.int("DATABASE_POOL_MAX", 10),
            acquireTimeoutMillis: env.int("DATABASE_POOL_ACQUIRE", 60000),
            createTimeoutMillis: env.int("DATABASE_POOL_CREATE", 30000),
            idleTimeoutMillis: env.int("DATABASE_POOL_IDLE", 30000),
        },
        debug: false,
    },
});
```

## Priority 2: Enhanced Monitoring & Testing

### 1. ELK Stack Configuration

```yaml
# docker-compose.elk.yml
version: "3.8"
services:
    elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
        environment:
            - discovery.type=single-node
            - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
        ports:
            - "9200:9200"
        volumes:
            - es_data:/usr/share/elasticsearch/data

    logstash:
        image: docker.elastic.co/logstash/logstash:8.11.0
        volumes:
            - ./logstash/pipeline:/usr/share/logstash/pipeline
        ports:
            - "5044:5044"
        depends_on:
            - elasticsearch

    kibana:
        image: docker.elastic.co/kibana/kibana:8.11.0
        ports:
            - "5601:5601"
        environment:
            - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
        depends_on:
            - elasticsearch

volumes:
    es_data:
```

### 2. Additional Shared Packages

#### @civicpoll/ui Package

```typescript
// packages/ui/src/components/Button.tsx
import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={buttonVariants({ variant, size, className })}
        ref={ref}
        {...props}
      />
    );
  }
);
```

#### @civicpoll/types Package

```typescript
// packages/types/src/index.ts
export interface User {
    id: number;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    role: UserRole;
    createdAt: string;
    updatedAt: string;
}

export interface Survey {
    id: number;
    title: string;
    description: string;
    status: SurveyStatus;
    startDate: string;
    endDate: string;
    questions: Question[];
    createdBy: User;
}

export interface GDPRRequest {
    id: number;
    type: "export" | "delete";
    userId: number;
    status: "pending" | "processing" | "completed";
    requestedAt: string;
    completedAt?: string;
}

export enum UserRole {
    Admin = "admin",
    User = "user",
    Moderator = "moderator",
}

export enum SurveyStatus {
    Draft = "draft",
    Active = "active",
    Closed = "closed",
    Archived = "archived",
}
```

### 3. Performance Testing Script

```javascript
// infrastructure/scripts/performance-baseline.js
import { check } from "k6";
import http from "k6/http";

export const options = {
    scenarios: {
        baseline: {
            executor: "constant-vus",
            vus: 10,
            duration: "5m",
        },
        stress: {
            executor: "ramping-vus",
            startVUs: 0,
            stages: [
                { duration: "2m", target: 50 },
                { duration: "5m", target: 50 },
                { duration: "2m", target: 100 },
                { duration: "5m", target: 100 },
                { duration: "2m", target: 0 },
            ],
        },
    },
    thresholds: {
        http_req_duration: ["p(95)<500", "p(99)<1000"],
        http_req_failed: ["rate<0.1"],
    },
};

export default function () {
    const responses = http.batch([
        ["GET", "https://civicpoll.fr.smatflow.xyz/"],
        ["GET", "https://civicpoll.fr.smatflow.xyz/api/health"],
    ]);

    responses.forEach((res, index) => {
        check(res, {
            "status is 200": (r) => r.status === 200,
            "response time < 500ms": (r) => r.timings.duration < 500,
        });
    });
}
```

## Priority 3: Documentation & Business Continuity

### 1. Documentation Structure

```
docs/
├── architecture/
│   ├── overview.md
│   ├── security.md
│   └── data-flow.md
├── runbooks/
│   ├── deployment.md
│   ├── rollback.md
│   ├── incident-response.md
│   └── disaster-recovery.md
├── api/
│   └── strapi-api.md
├── user-guides/
│   ├── admin-guide.md
│   └── end-user-guide.md
└── videos/
    └── tutorials.md
```

### 2. Disaster Recovery Plan Template

```markdown
# Disaster Recovery Plan

## Recovery Objectives

- **RTO (Recovery Time Objective):** 4 hours
- **RPO (Recovery Point Objective):** 1 hour

## Incident Classification

1. **Critical:** Complete system failure
2. **Major:** Partial system failure affecting > 50% users
3. **Minor:** Isolated component failure

## Recovery Procedures

### Database Recovery

1. Stop affected services
2. Restore from latest backup
3. Apply transaction logs
4. Verify data integrity
5. Restart services

### Application Recovery

1. Switch to backup server
2. Deploy last known good version
3. Restore configuration
4. Verify functionality
5. Switch DNS if needed

## Contact Information

- On-call Engineer: [Phone]
- Team Lead: [Phone]
- Management: [Phone]
```

### 3. Health Check Endpoints

```typescript
// apps/backend/src/api/health/controllers/health.ts
export default {
    async check(ctx) {
        const checks = {
            status: "healthy",
            timestamp: new Date().toISOString(),
            services: {
                database: await this.checkDatabase(),
                redis: await this.checkRedis(),
                email: await this.checkEmail(),
            },
        };

        const unhealthy = Object.values(checks.services).some((s) => !s.healthy);
        ctx.status = unhealthy ? 503 : 200;
        ctx.body = checks;
    },

    async checkDatabase() {
        try {
            await strapi.db.connection.raw("SELECT 1");
            return { healthy: true, latency: Date.now() - start };
        } catch (error) {
            return { healthy: false, error: error.message };
        }
    },

    async checkRedis() {
        try {
            const start = Date.now();
            await strapi.redis.ping();
            return { healthy: true, latency: Date.now() - start };
        } catch (error) {
            return { healthy: false, error: error.message };
        }
    },
};
```

## Implementation Timeline

### Additional Time Required

- **Priority 1 Items:** ***** days
- **Priority 2 Items:** **** days
- **Priority 3 Items:** **** days

**Total Additional Time:** 16-22 days

This would extend Phase 0 from 4-6 weeks to approximately 7-9 weeks.

## Recommendation

1. **Immediate Implementation (Week 1-2):**

    - Server hardening
    - WAF configuration
    - Connection pooling
    - Health checks

2. **Progressive Enhancement (Week 3-4):**

    - ELK stack setup
    - Additional shared packages
    - Performance baselines

3. **Final Polish (Week 5):**
    - Documentation completion
    - Disaster recovery testing
    - Final security audit

By implementing these enhancements, the CivicPoll platform will have enterprise-grade security, monitoring, and reliability suitable for a government civic engagement platform.
