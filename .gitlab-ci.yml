stages:
  - install
  - lint
  - test
  - build
  - security
  - deploy

variables:
  NODE_VERSION: "20"
  PNPM_VERSION: "8"
  FF_USE_FASTZIP: "true"
  ARTIFACT_COMPRESSION_LEVEL: "fast"
  CACHE_COMPRESSION_LEVEL: "fast"

# Cache configuration
.cache_config: &cache_config
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
    policy: pull-push

# Default job configuration
default:
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache libc6-compat python3 make g++
    - corepack enable
    - corepack prepare pnpm@${PNPM_VERSION} --activate
    - pnpm config set store-dir .pnpm-store

# Install dependencies
install:
  stage: install
  <<: *cache_config
  script:
    - pnpm install --frozen-lockfile
  artifacts:
    paths:
      - node_modules/
      - apps/*/node_modules/
      - packages/*/node_modules/
      - .pnpm-store/
    expire_in: 1 hour

# Linting
lint:
  stage: lint
  needs: ["install"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm run lint
  artifacts:
    reports:
      codequality: lint-report.json
    expire_in: 1 week
  allow_failure: true

# Type checking
typecheck:
  stage: lint
  needs: ["install"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm run typecheck

# Unit tests
test:unit:
  stage: test
  needs: ["install"]
  services:
    - postgres:14-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    DATABASE_HOST: postgres
    DATABASE_CLIENT: postgres
    REDIS_HOST: redis
    NODE_ENV: test
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm run test:unit -- --coverage
  coverage: '/Coverage: \d+\.\d+%/'
  artifacts:
    when: always
    reports:
      junit:
        - apps/*/junit.xml
        - packages/*/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week

# Integration tests
test:integration:
  stage: test
  needs: ["install"]
  services:
    - postgres:14-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    DATABASE_HOST: postgres
    DATABASE_CLIENT: postgres
    REDIS_HOST: redis
    NODE_ENV: test
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm run test:integration
  allow_failure: true

# Security scan - Dependencies
security:dependencies:
  stage: security
  needs: ["install"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm audit --production --audit-level=high
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json
  allow_failure: true

# Security scan - SAST
security:sast:
  stage: security
  needs: ["install"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - pnpm add -D @microsoft/eslint-plugin-sdl
    - pnpm run lint:security || true
  artifacts:
    reports:
      sast: gl-sast-report.json
  allow_failure: true

# Build Backend
build:backend:
  stage: build
  needs: ["install", "test:unit"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  script:
    - cd apps/backend
    - pnpm run build
  artifacts:
    paths:
      - apps/backend/build/
      - apps/backend/dist/
    expire_in: 1 week

# Build Frontend
build:frontend:
  stage: build
  needs: ["install", "test:unit"]
  cache:
    policy: pull
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  variables:
    NEXT_PUBLIC_API_URL: ${CI_COMMIT_REF_NAME == 'main' ? 'https://api.civicpoll.fr' : 'https://staging-api.civicpoll.fr'}
    NEXT_PUBLIC_FRONTEND_URL: ${CI_COMMIT_REF_NAME == 'main' ? 'https://civicpoll.fr' : 'https://staging.civicpoll.fr'}
  script:
    - cd apps/frontend
    - pnpm run build
  artifacts:
    paths:
      - apps/frontend/.next/
      - apps/frontend/public/
    expire_in: 1 week

# Docker build
build:docker:
  stage: build
  image: docker:24-alpine
  services:
    - docker:24-dind
  needs: ["build:backend", "build:frontend"]
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -f infrastructure/docker/backend.Dockerfile -t $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA .
    - docker build -f infrastructure/docker/frontend.Dockerfile -t $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
    # Tag as latest for main branch
    - |
      if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        docker tag $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/backend:latest
        docker tag $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/frontend:latest
        docker push $CI_REGISTRY_IMAGE/backend:latest
        docker push $CI_REGISTRY_IMAGE/frontend:latest
      fi
  only:
    - main
    - develop
    - /^release\/.*$/

# Deploy to staging
deploy:staging:
  stage: deploy
  image: alpine:latest
  needs: ["build:docker"]
  before_script:
    - apk add --no-cache openssh-client rsync curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $STAGING_HOST >> ~/.ssh/known_hosts
  script:
    - |
      ssh $DEPLOY_USER@$STAGING_HOST << 'EOF'
        cd /opt/civicpoll
        git pull origin develop
        docker-compose -f infrastructure/docker/docker-compose.yml pull
        docker-compose -f infrastructure/docker/docker-compose.yml up -d
        
        # Run migrations
        docker-compose -f infrastructure/docker/docker-compose.yml exec -T backend pnpm strapi migration:run
        
        # Health check
        sleep 30
        curl -f https://staging.civicpoll.fr/api/health || exit 1
      EOF
  only:
    - develop
  environment:
    name: staging
    url: https://staging.civicpoll.fr

# Deploy to production
deploy:production:
  stage: deploy
  image: alpine:latest
  needs: ["build:docker"]
  before_script:
    - apk add --no-cache openssh-client rsync curl
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
  script:
    - |
      ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
        cd /opt/civicpoll
        
        # Backup current deployment
        ./infrastructure/scripts/backup.sh
        
        # Pull latest code
        git pull origin main
        pnpm install --frozen-lockfile
        pnpm run build
        
        # Deploy with zero downtime
        pm2 reload ecosystem.config.js --update-env
        
        # Run migrations
        cd apps/backend
        NODE_ENV=production pnpm strapi migration:run
        
        # Health check
        sleep 10
        curl -f https://civicpoll.fr.smatflow.xyz/api/health || exit 1
      EOF
  only:
    - main
  when: manual
  environment:
    name: production
    url: https://civicpoll.fr.smatflow.xyz

# Cleanup old docker images
cleanup:docker:
  stage: .post
  image: docker:24-alpine
  services:
    - docker:24-dind
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - |
      # Keep only the last 5 images
      IMAGES=$(docker images $CI_REGISTRY_IMAGE/* --format "{{.Repository}}:{{.Tag}}" | grep -v latest | sort -r | tail -n +6)
      for image in $IMAGES; do
        docker rmi $image || true
      done
  only:
    - schedules
  allow_failure: true