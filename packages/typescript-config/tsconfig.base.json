{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ES2022", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "allowJs": true, "incremental": true, "tsBuildInfoFile": "node_modules/.cache/tsbuildinfo.json", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules", "dist", "build", ".next", ".turbo"]}